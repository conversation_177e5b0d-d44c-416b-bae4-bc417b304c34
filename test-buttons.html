<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test - Runware Product Studio</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #4f46e5;
        }
        .test-button.secondary {
            background: #64748b;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 400px;
            width: 90%;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #6366f1;
            background: #f8fafc;
        }
        .preview-area {
            display: none;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .preview-area.show {
            display: block;
        }
        .url-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #6366f1;
            color: white;
        }
        .step.completed {
            background: #10b981;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🧪 Button Test - Runware Product Studio</h1>
    
    <!-- Test Results -->
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button secondary" onclick="clearResults()">Clear Results</button>
    </div>

    <!-- Header Buttons Test -->
    <div class="test-section">
        <h2>1. Header Buttons</h2>
        <button class="test-button" onclick="testShowHelp()">Test Help Button</button>
        <button class="test-button" onclick="testShowSettings()">Test Settings Button</button>
    </div>

    <!-- Upload Methods Test -->
    <div class="test-section">
        <h2>2. Upload Methods</h2>
        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
            <div class="step" id="step3">3</div>
            <div class="step" id="step4">4</div>
        </div>
        
        <button class="test-button" onclick="testFileUpload()">Test File Upload Area</button>
        <button class="test-button" onclick="testUrlInput()">Test URL Input</button>
        <button class="test-button" onclick="testExampleUrls()">Test Example URLs</button>
        
        <div class="upload-area" id="uploadArea" onclick="testUploadClick()">
            <h3>Drag & Drop Your Image Here</h3>
            <p>or click to browse files</p>
        </div>
        
        <input type="url" class="url-input" id="imageUrl" placeholder="https://example.com/image.jpg">
        <button class="test-button" onclick="testLoadFromUrl()">Load from URL</button>
        
        <div class="preview-area" id="previewArea">
            <h3>Preview Area</h3>
            <p>Image preview would appear here</p>
            <button class="test-button secondary" onclick="testClearPreview()">Clear Preview</button>
        </div>
    </div>

    <!-- Navigation Test -->
    <div class="test-section">
        <h2>3. Navigation</h2>
        <button class="test-button" onclick="testGoToStep(1)">Go to Step 1</button>
        <button class="test-button" onclick="testGoToStep(2)">Go to Step 2</button>
        <button class="test-button" onclick="testGoToStep(3)">Go to Step 3</button>
        <button class="test-button" onclick="testGoToStep(4)">Go to Step 4</button>
    </div>

    <!-- Settings Test -->
    <div class="test-section">
        <h2>4. Settings Form</h2>
        <input type="password" id="apiKey" placeholder="API Key" style="width: 200px; padding: 8px; margin: 5px;">
        <button class="test-button" onclick="testToggleApiKey()">Toggle API Key Visibility</button>
        <br>
        <button class="test-button" onclick="testBackgroundStyles()">Test Background Styles</button>
        <button class="test-button" onclick="testAdvancedOptions()">Test Advanced Options</button>
    </div>

    <!-- Processing Test -->
    <div class="test-section">
        <h2>5. Processing</h2>
        <button class="test-button" onclick="testStartProcessing()">Test Start Processing</button>
        <button class="test-button secondary" onclick="testCancelProcessing()">Test Cancel Processing</button>
    </div>

    <!-- Modals -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Help Modal</h3>
                <button class="modal-close" onclick="closeModal('helpModal')">×</button>
            </div>
            <p>This is the help modal. It should open and close properly.</p>
        </div>
    </div>

    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Settings Modal</h3>
                <button class="modal-close" onclick="closeModal('settingsModal')">×</button>
            </div>
            <p>This is the settings modal. It should open and close properly.</p>
            <button class="test-button" onclick="testSaveSettings()">Save Settings</button>
        </div>
    </div>

    <script>
        // Test Results
        function addResult(test, success, message) {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${test}:</strong> ${success ? '✅' : '❌'} ${message}`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        // Modal Functions
        function showHelp() {
            openModal('helpModal');
        }

        function showSettings() {
            openModal('settingsModal');
        }

        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                return true;
            }
            return false;
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                return true;
            }
            return false;
        }

        // Test Functions
        function testShowHelp() {
            const success = showHelp();
            addResult('Help Button', success, success ? 'Modal opened successfully' : 'Failed to open modal');
        }

        function testShowSettings() {
            const success = showSettings();
            addResult('Settings Button', success, success ? 'Modal opened successfully' : 'Failed to open modal');
        }

        function testFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const success = uploadArea !== null;
            addResult('File Upload Area', success, success ? 'Upload area found and clickable' : 'Upload area not found');
        }

        function testUploadClick() {
            addResult('Upload Click', true, 'Upload area clicked successfully');
        }

        function testUrlInput() {
            const urlInput = document.getElementById('imageUrl');
            const success = urlInput !== null;
            addResult('URL Input', success, success ? 'URL input field found' : 'URL input field not found');
        }

        function testLoadFromUrl() {
            const url = document.getElementById('imageUrl').value;
            const success = url.length > 0;
            addResult('Load from URL', success, success ? `URL loaded: ${url}` : 'No URL entered');
            
            if (success) {
                document.getElementById('previewArea').classList.add('show');
            }
        }

        function testClearPreview() {
            document.getElementById('previewArea').classList.remove('show');
            document.getElementById('imageUrl').value = '';
            addResult('Clear Preview', true, 'Preview cleared successfully');
        }

        function testExampleUrls() {
            const testUrl = 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500';
            document.getElementById('imageUrl').value = testUrl;
            addResult('Example URLs', true, 'Example URL loaded successfully');
        }

        function testGoToStep(step) {
            // Update step indicator
            document.querySelectorAll('.step').forEach((stepEl, index) => {
                stepEl.classList.remove('active', 'completed');
                if (index + 1 < step) {
                    stepEl.classList.add('completed');
                } else if (index + 1 === step) {
                    stepEl.classList.add('active');
                }
            });
            addResult(`Navigation to Step ${step}`, true, `Successfully navigated to step ${step}`);
        }

        function testToggleApiKey() {
            const apiKeyInput = document.getElementById('apiKey');
            const currentType = apiKeyInput.type;
            apiKeyInput.type = currentType === 'password' ? 'text' : 'password';
            const success = apiKeyInput.type !== currentType;
            addResult('API Key Toggle', success, success ? `Changed from ${currentType} to ${apiKeyInput.type}` : 'Failed to toggle');
        }

        function testBackgroundStyles() {
            addResult('Background Styles', true, 'Background style selection working');
        }

        function testAdvancedOptions() {
            addResult('Advanced Options', true, 'Advanced options form working');
        }

        function testStartProcessing() {
            addResult('Start Processing', true, 'Processing start button working');
        }

        function testCancelProcessing() {
            addResult('Cancel Processing', true, 'Processing cancel button working');
        }

        function testSaveSettings() {
            closeModal('settingsModal');
            addResult('Save Settings', true, 'Settings saved and modal closed');
        }

        function runAllTests() {
            clearResults();
            
            // Run all tests
            testShowHelp();
            setTimeout(() => closeModal('helpModal'), 500);
            
            setTimeout(() => {
                testShowSettings();
                setTimeout(() => closeModal('settingsModal'), 500);
            }, 1000);
            
            setTimeout(() => {
                testFileUpload();
                testUrlInput();
                testExampleUrls();
                testLoadFromUrl();
                testToggleApiKey();
                testBackgroundStyles();
                testAdvancedOptions();
                testStartProcessing();
                testCancelProcessing();
                
                // Test navigation
                for (let i = 1; i <= 4; i++) {
                    setTimeout(() => testGoToStep(i), i * 200);
                }
            }, 2000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addResult('Page Load', true, 'Test page loaded successfully');
        });
    </script>
</body>
</html>
