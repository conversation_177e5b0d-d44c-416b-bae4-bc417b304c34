{"name": "runware-product-studio", "version": "1.0.0", "description": "Professional product photography using Runware API", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "build": "echo 'No build step required for vanilla JS'", "clean": "rimraf backend/uploads/input/* backend/uploads/output/*", "test": "echo 'No tests specified'"}, "keywords": ["runware", "product-photography", "ai", "image-processing", "e-commerce"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.6.0", "uuid": "^9.0.1", "sharp": "^0.32.6", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "rimraf": "^5.0.5"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/runware-product-studio.git"}, "bugs": {"url": "https://github.com/yourusername/runware-product-studio/issues"}, "homepage": "https://github.com/yourusername/runware-product-studio#readme"}