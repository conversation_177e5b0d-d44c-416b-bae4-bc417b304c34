const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// File filter
const fileFilter = (req, file, cb) => {
  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,webp').split(',');
  const fileExt = path.extname(file.originalname).toLowerCase().slice(1);
  
  if (allowedTypes.includes(fileExt)) {
    cb(null, true);
  } else {
    cb(new Error(`File type .${fileExt} not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

// Storage configuration
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/input');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueId = uuidv4();
    const ext = path.extname(file.originalname).toLowerCase();
    const filename = `${uniqueId}${ext}`;
    cb(null, filename);
  }
});

// Multer configuration
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    files: 1
  }
});

// POST /api/upload - Single file upload
router.post('/', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please select an image file to upload'
      });
    }

    const fileInfo = {
      id: path.parse(req.file.filename).name,
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      uploadedAt: new Date().toISOString(),
      url: `/uploads/input/${req.file.filename}`
    };

    console.log(`📤 File uploaded: ${req.file.originalname} (${(req.file.size / 1024 / 1024).toFixed(2)}MB)`);

    res.json({
      success: true,
      message: 'File uploaded successfully',
      file: fileInfo
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      error: 'Upload failed',
      message: error.message
    });
  }
});

// POST /api/upload/batch - Multiple file upload
router.post('/batch', upload.array('images', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select image files to upload'
      });
    }

    const filesInfo = req.files.map(file => ({
      id: path.parse(file.filename).name,
      filename: file.filename,
      originalName: file.originalname,
      path: file.path,
      size: file.size,
      mimetype: file.mimetype,
      uploadedAt: new Date().toISOString(),
      url: `/uploads/input/${file.filename}`
    }));

    const totalSize = req.files.reduce((sum, file) => sum + file.size, 0);
    console.log(`📤 Batch upload: ${req.files.length} files (${(totalSize / 1024 / 1024).toFixed(2)}MB total)`);

    res.json({
      success: true,
      message: `${req.files.length} files uploaded successfully`,
      files: filesInfo,
      summary: {
        count: req.files.length,
        totalSize,
        totalSizeFormatted: `${(totalSize / 1024 / 1024).toFixed(2)}MB`
      }
    });

  } catch (error) {
    console.error('Batch upload error:', error);
    res.status(500).json({
      error: 'Batch upload failed',
      message: error.message
    });
  }
});

// GET /api/upload/info - Get upload configuration
router.get('/info', (req, res) => {
  res.json({
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760,
    maxFileSizeFormatted: `${((parseInt(process.env.MAX_FILE_SIZE) || 10485760) / 1024 / 1024).toFixed(0)}MB`,
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,webp').split(','),
    maxBatchSize: 10,
    uploadEndpoints: {
      single: '/api/upload',
      batch: '/api/upload/batch'
    }
  });
});

// DELETE /api/upload/:filename - Delete uploaded file
router.delete('/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../uploads/input', filename);
    
    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({
        error: 'File not found',
        filename
      });
    }

    // Delete file
    await fs.unlink(filePath);
    console.log(`🗑️ File deleted: ${filename}`);

    res.json({
      success: true,
      message: 'File deleted successfully',
      filename
    });

  } catch (error) {
    console.error('Delete error:', error);
    res.status(500).json({
      error: 'Failed to delete file',
      message: error.message
    });
  }
});

// GET /api/upload/cleanup - Manual cleanup old files
router.post('/cleanup', async (req, res) => {
  try {
    const inputDir = path.join(__dirname, '../uploads/input');
    const outputDir = path.join(__dirname, '../uploads/output');
    const maxAge = parseInt(process.env.AUTO_CLEANUP_HOURS) || 24; // hours
    const cutoff = Date.now() - (maxAge * 60 * 60 * 1000);
    
    let deletedCount = 0;
    let totalSize = 0;

    // Cleanup function
    const cleanupDirectory = async (dir) => {
      try {
        const files = await fs.readdir(dir);
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime.getTime() < cutoff) {
            await fs.unlink(filePath);
            deletedCount++;
            totalSize += stats.size;
            console.log(`🗑️ Cleaned up: ${file}`);
          }
        }
      } catch (error) {
        console.error(`Cleanup error in ${dir}:`, error.message);
      }
    };

    await cleanupDirectory(inputDir);
    await cleanupDirectory(outputDir);

    res.json({
      success: true,
      message: 'Cleanup completed',
      deletedFiles: deletedCount,
      freedSpace: totalSize,
      freedSpaceFormatted: `${(totalSize / 1024 / 1024).toFixed(2)}MB`,
      maxAge: `${maxAge} hours`
    });

  } catch (error) {
    console.error('Cleanup error:', error);
    res.status(500).json({
      error: 'Cleanup failed',
      message: error.message
    });
  }
});

module.exports = router;