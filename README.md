# 🎨 Runware Product Studio

Professional AI-powered product photography using Runware API. Transform your product images with background removal, quality enhancement, and stunning background generation.

![Runware Product Studio](https://img.shields.io/badge/Product-Photography-blue) ![Node.js](https://img.shields.io/badge/Node.js-16%2B-green) ![License](https://img.shields.io/badge/License-MIT-yellow)

## ✨ Features

### 🔥 Core Capabilities
- **🖼️ Smart Background Removal** - AI-powered product isolation with perfect edges
- **📈 Image Enhancement** - Upscale 2x-4x with quality improvement
- **🎨 Background Generation** - 8 professional styles (Minimalist, Luxury, Natural, etc.)
- **⚡ Real-time Processing** - Live progress tracking and status updates
- **📱 Responsive Design** - Works perfectly on desktop and mobile
- **🔄 Batch Processing** - Process multiple images at once

### 🛠️ Technical Features
- **📤 Flexible Upload** - Drag & drop files or paste image URLs
- **🔐 Secure API** - Local API key storage with encryption
- **💰 Cost Tracking** - Real-time cost estimation and tracking
- **📊 Progress Monitoring** - Step-by-step processing visualization
- **💾 Auto Cleanup** - Automatic file management and storage
- **🚀 Fast Processing** - Optimized for speed and reliability

## 🎯 Workflow

```
1. Upload Product Image → 2. Configure Settings → 3. AI Processing → 4. Download Results
   📤 File/URL              ⚙️ Style & Quality      🤖 3-Step Pipeline   📥 Professional Images
```

### Processing Pipeline:
1. **Background Removal** - Smart product extraction
2. **Image Enhancement** - Quality upscaling and improvement  
3. **Background Generation** - Professional scene creation
4. **Final Polish** - Optional additional enhancement

## 🚀 Quick Start

### Prerequisites
- **Node.js 16+** - [Download](https://nodejs.org/)
- **Runware API Key** - [Get yours](https://runware.ai/)
- **NPM/Yarn** - Package manager

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd runware-product-studio
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
```

3. **Environment setup**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
nano .env
```

4. **Configure environment variables**
```env
# Required
RUNWARE_API_KEY=your_runware_api_key_here
PORT=3000

# Optional (defaults provided)
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,webp
```

5. **Start the application**
```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

6. **Open in browser**
```
http://localhost:3000
```

## 📖 Usage Guide

### Step 1: Upload Image
- **📁 File Upload**: Drag & drop or click to browse
- **🔗 URL Method**: Paste direct image links
- **✅ Supported**: JPG, PNG, WEBP (max 10MB)

### Step 2: Configure Settings
- **🔑 API Key**: Enter your Runware API key
- **🎨 Background Style**: Choose from 8 professional styles:
  - **🤍 Minimalist** - Clean white backgrounds
  - **✨ Luxury** - Premium marble surfaces  
  - **🌿 Natural** - Warm wooden textures
  - **🏢 Modern** - Contemporary concrete
  - **🏠 Lifestyle** - Cozy home settings
  - **🌅 Outdoor** - Natural environments
  - **📸 Studio** - Professional setups
  - **🎨 Abstract** - Creative geometric patterns

- **⚙️ Advanced Options**:
  - Image Enhancement: 1x-4x upscaling
  - Background Variants: 2-8 different images
  - Final Polish: Extra enhancement for top results

### Step 3: Processing
- **👀 Real-time Progress** - Watch each step complete
- **💰 Cost Tracking** - See estimated and actual costs
- **⏱️ Time Monitoring** - Track processing duration
- **🛑 Cancel Option** - Stop processing anytime

### Step 4: Results
- **🖼️ Gallery View** - All generated images
- **📊 Processing Summary** - Time, cost, and image count
- **💾 Download Options** - Individual or bulk download
- **🔍 Full-size Preview** - View images in detail

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `RUNWARE_API_KEY` | Your Runware API key | - | ✅ |
| `PORT` | Server port | 3000 | ❌ |
| `NODE_ENV` | Environment mode | development | ❌ |
| `MAX_FILE_SIZE` | Max upload size (bytes) | 10485760 | ❌ |
| `ALLOWED_FILE_TYPES` | Allowed file extensions | jpg,jpeg,png,webp | ❌ |
| `RATE_LIMIT_MAX_REQUESTS` | API rate limit | 100 | ❌ |
| `AUTO_CLEANUP_HOURS` | File cleanup interval | 24 | ❌ |

### Application Settings
- **🔐 API Key Storage** - Save keys locally (browser storage)
- **🧹 Auto Cleanup** - Automatic file cleanup after processing
- **📱 Responsive Mode** - Optimize for mobile devices

## 🏗️ Architecture

### Backend (Node.js + Express)
```
backend/
├── server.js              # Main server
├── routes/                # API endpoints
│   ├── upload.js          # File upload handling
│   ├── process.js         # Image processing
│   └── status.js          # Job status tracking
├── services/              # Business logic
│   ├── runware.js         # Runware API integration
│   └── fileManager.js     # File management
└── uploads/               # Temporary storage
    ├── input/             # Uploaded files
    └── output/            # Processed results
```

### Frontend (Vanilla JS)
```
frontend/
├── index.html             # Main UI
├── styles.css             # Modern styling
├── script.js              # App logic
└── assets/                # Static files
```

### API Endpoints
- `POST /api/upload` - Single file upload
- `POST /api/upload/batch` - Multiple file upload  
- `POST /api/process/single` - Process single image
- `POST /api/process/batch` - Process multiple images
- `GET /api/status/:jobId` - Get processing status
- `GET /api/process/styles` - Get background styles
- `GET /api/health` - Health check

## 💰 Cost Information

### Typical Costs (per image):
- **Background Removal**: ~$0.006
- **Image Upscaling**: ~$0.002
- **Background Generation**: ~$0.008-0.032 (varies by variant count)
- **Total Workflow**: ~$0.02-0.05 per product

### Cost Optimization Tips:
- Use 2x upscaling for most products (good quality/cost balance)
- Generate 4 variants (recommended for variety)
- Enable final polish only for premium products
- Use batch processing for multiple images

## 🚨 Troubleshooting

### Common Issues

**❌ API Key Error**
```
Error: API key validation failed
```
- Verify your Runware API key is correct
- Check if you have sufficient credits
- Ensure key has proper permissions

**❌ Upload Failed**
```
Error: File too large
```
- Maximum file size is 10MB
- Supported formats: JPG, PNG, WEBP
- Try compressing image before upload

**❌ Processing Stuck**
```
Error: Processing timeout
```
- Check your internet connection
- Verify Runware service status
- Try with a smaller image
- Restart the application

**❌ Port Already in Use**
```
Error: EADDRINUSE :::3000
```
- Change PORT in .env file
- Kill existing process: `lsof -ti:3000 | xargs kill`
- Use different port: `PORT=3001 npm start`

### Debug Mode
```bash
# Enable verbose logging
DEBUG=* npm run dev

# Check application logs
tail -f logs/app.log

# Monitor API responses
npm run dev --verbose
```

## 🛡️ Security

### Best Practices
- **🔐 API Keys** - Never commit API keys to version control
- **📁 File Storage** - Automatic cleanup of uploaded files
- **🚦 Rate Limiting** - Built-in API rate limiting
- **🛡️ Input Validation** - File type and size validation
- **🔒 CORS** - Configurable CORS settings

### Production Deployment
- Use HTTPS in production
- Set strong SESSION_SECRET
- Configure proper CORS_ORIGIN
- Enable request logging
- Set up monitoring and alerts

## 🧪 Development

### Scripts
```bash
npm run dev          # Development with auto-reload
npm start           # Production server
npm run clean       # Clean uploaded files
npm run test        # Run tests (when available)
```

### File Structure
```
runware-product-studio/
├── 📁 backend/                 # Server-side code
├── 📁 frontend/                # Client-side code
├── 📁 config/                  # Configuration files
├── 📄 package.json            # Dependencies
├── 🔐 .env                    # Environment variables
└── 📚 README.md               # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Runware** - For providing the amazing AI API
- **Contributors** - Thanks to all contributors
- **Community** - For feedback and feature requests

## 📞 Support

- **📧 Issues** - [GitHub Issues](issues-url)
- **📖 Documentation** - [Runware Docs](https://runware.ai/docs)
- **💬 Community** - [Runware Discord](https://discord.com/invite/aJ4UzvBqNU)

---

## 🚀 Ready to Transform Your Product Photos?

Get started in 3 steps:
1. **🔑 Get API Key** → [Sign up at Runware](https://runware.ai)
2. **⚡ Install & Run** → `npm install && npm start`  
3. **🎨 Upload & Process** → Transform your products!

**Happy Creating! 🎉**