{"app": {"name": "Runware Product Studio", "version": "1.0.0", "description": "Professional AI-powered product photography"}, "server": {"port": 3000, "host": "localhost", "environment": "development"}, "runware": {"baseUrl": "https://api.runware.ai/v1", "timeout": 60000, "retryAttempts": 3, "defaultModels": {"backgroundRemoval": "runware:112@5", "imageGeneration": "runware:100@1", "controlNet": "runware:101@1", "productLora": "runware:120@2"}}, "upload": {"maxFileSize": 10485760, "allowedTypes": ["jpg", "jpeg", "png", "webp"], "uploadDir": "backend/uploads", "maxBatchSize": 10}, "processing": {"defaultSettings": {"backgroundStyle": "minimalist", "upscaleFactor": 2, "generateCount": 4, "finalPolish": false}, "limits": {"maxUpscaleFactor": 4, "maxGenerateCount": 8, "maxImageSize": 4096}, "timeout": 300000}, "security": {"rateLimiting": {"windowMs": 900000, "maxRequests": 100}, "cors": {"origin": "http://localhost:3000", "credentials": true}, "helmet": {"contentSecurityPolicy": {"directives": {"defaultSrc": ["'self'"], "styleSrc": ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"], "fontSrc": ["'self'", "https://fonts.gstatic.com"], "imgSrc": ["'self'", "data:", "https:", "blob:"], "scriptSrc": ["'self'", "'unsafe-inline'"], "connectSrc": ["'self'", "https://api.runware.ai"]}}}}, "storage": {"autoCleanup": {"enabled": true, "intervalMinutes": 60, "maxAgeHours": 24}, "maxDiskUsage": "1GB"}, "logging": {"level": "info", "format": "combined", "file": "logs/app.log"}, "features": {"batchProcessing": true, "realTimeProgress": true, "costTracking": true, "downloadAll": true, "previewMode": true}, "ui": {"theme": "modern", "showCosts": true, "showProgress": true, "autoSaveSettings": true, "maxPreviewSize": "500px"}, "backgroundStyles": {"minimalist": {"name": "Minimalist", "description": "Clean white background with soft shadows", "prompt": "clean white background, minimalist studio, soft shadows, product photography, professional lighting", "preview": "🤍"}, "luxury": {"name": "Luxury", "description": "Premium marble surface with elegant lighting", "prompt": "luxury marble surface, elegant lighting, premium feel, high-end photography, sophisticated ambiance", "preview": "✨"}, "natural": {"name": "Natural", "description": "Wooden surface with warm natural lighting", "prompt": "natural wooden table, soft natural lighting, organic feel, lifestyle photography, warm tones", "preview": "🌿"}, "modern": {"name": "Modern", "description": "Contemporary concrete with industrial feel", "prompt": "modern concrete surface, contemporary lighting, industrial feel, architectural photography, clean lines", "preview": "🏢"}, "lifestyle": {"name": "Lifestyle", "description": "Cozy home setting with warm atmosphere", "prompt": "cozy home setting, warm lighting, lifestyle photography, lived-in feel, comfortable atmosphere", "preview": "🏠"}, "outdoor": {"name": "Outdoor", "description": "Natural outdoor environment with soft daylight", "prompt": "natural outdoor setting, soft daylight, fresh environment, natural photography, scenic background", "preview": "🌅"}, "studio": {"name": "Studio", "description": "Professional photography studio setup", "prompt": "professional photography studio, softbox lighting, seamless backdrop, commercial photography", "preview": "📸"}, "abstract": {"name": "Abstract", "description": "Modern geometric and artistic backgrounds", "prompt": "abstract geometric background, modern design, artistic composition, creative photography", "preview": "🎨"}}}