# Runware API Configuration
RUNWARE_API_KEY=your_runware_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=jpg,jpeg,png,webp
UPLOAD_DIR=backend/uploads

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ORIGIN=http://localhost:3000
SESSION_SECRET=your_session_secret_here

# Image Processing
DEFAULT_OUTPUT_QUALITY=95
DEFAULT_UPSCALE_FACTOR=2
MAX_BACKGROUND_VARIANTS=8

# Model Configuration (Advanced)
DEFAULT_BACKGROUND_REMOVAL_MODEL=runware:112@5
DEFAULT_UPSCALE_MODEL=runware:109@1
DEFAULT_IMAGE_GENERATION_MODEL=civitai:4201@130090

# Cache Configuration
ENABLE_CACHE=true
CACHE_TTL=3600

# Cleanup Configuration
AUTO_CLEANUP_HOURS=24  # Delete files older than 24 hours
CLEANUP_INTERVAL_MINUTES=60  # Run cleanup every hour

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Optional: Database (for job tracking)
# DATABASE_URL=sqlite:./data/jobs.db

# Optional: Redis (for job queue)
# REDIS_URL=redis://localhost:6379