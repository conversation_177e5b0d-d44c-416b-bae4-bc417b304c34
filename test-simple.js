#!/usr/bin/env node

/**
 * Simple test to verify basic functionality
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_API_KEY = process.env.RUNWARE_API_KEY || 'test_key_123';
const TEST_IMAGE_URL = 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=500';

async function testBasicFlow() {
    console.log('🧪 Testing Basic Flow...\n');

    try {
        // Test 1: Check server health
        console.log('1️⃣ Testing server health...');
        const healthResponse = await axios.get(`${BASE_URL}/api/health`);
        console.log('✅ Server is healthy:', healthResponse.data.status);

        // Test 2: Test API key validation
        console.log('\n2️⃣ Testing API key validation...');
        try {
            const testResponse = await axios.post(`${BASE_URL}/api/process/test`, {
                apiKey: TEST_API_KEY
            });
            console.log('✅ API key test passed:', testResponse.data.message);
        } catch (error) {
            console.log('⚠️ API key test failed (expected):', error.response?.data?.message);
        }

        // Test 3: Test background styles
        console.log('\n3️⃣ Testing background styles...');
        const stylesResponse = await axios.get(`${BASE_URL}/api/process/styles`);
        console.log('✅ Background styles loaded:', Object.keys(stylesResponse.data.styles).length);

        // Test 4: Test image processing (will fail without real API key)
        console.log('\n4️⃣ Testing image processing...');
        try {
            const processResponse = await axios.post(`${BASE_URL}/api/process/single`, {
                apiKey: TEST_API_KEY,
                imageUrl: TEST_IMAGE_URL,
                backgroundStyle: 'minimalist',
                upscaleFactor: 2,
                generateCount: 4,
                finalPolish: false,
                useInpainting: true
            });

            console.log('✅ Processing started:', processResponse.data.jobId);
            
            // Test status polling
            const jobId = processResponse.data.jobId;
            console.log('\n5️⃣ Testing status polling...');
            
            let attempts = 0;
            const maxAttempts = 5;
            
            while (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                try {
                    const statusResponse = await axios.get(`${BASE_URL}/api/status/${jobId}`);
                    const status = statusResponse.data;
                    
                    console.log(`   Status check ${attempts + 1}: ${status.status} (${status.progress?.percentage || 0}%)`);
                    
                    if (status.status === 'completed') {
                        console.log('✅ Processing completed successfully!');
                        console.log('   Results:', status.result?.results?.final?.length || 0, 'images');
                        break;
                    } else if (status.status === 'failed') {
                        console.log('❌ Processing failed:', status.error);
                        break;
                    }
                    
                } catch (statusError) {
                    console.log('⚠️ Status check failed:', statusError.response?.data?.message);
                    break;
                }
                
                attempts++;
            }
            
        } catch (error) {
            console.log('⚠️ Processing failed (expected without real API key):', error.response?.data?.message);
        }

        console.log('\n🎉 Basic flow test completed!');
        
        console.log('\n📋 Summary:');
        console.log('   ✅ Server health check: PASS');
        console.log('   ✅ Background styles: PASS');
        console.log('   ⚠️ API key validation: Needs real key');
        console.log('   ⚠️ Image processing: Needs real key');
        
        console.log('\n💡 Next steps:');
        console.log('   1. Add your real Runware API key to test processing');
        console.log('   2. Open http://localhost:3000 in browser');
        console.log('   3. Try uploading an image and processing it');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('\n🚨 Server is not running! Run: npm start');
        }
    }
}

// Test frontend assets
async function testFrontendAssets() {
    console.log('\n🌐 Testing Frontend Assets...\n');

    const assets = [
        { name: 'HTML', path: '/' },
        { name: 'CSS', path: '/styles.css' },
        { name: 'JavaScript', path: '/script.js' }
    ];

    for (const asset of assets) {
        try {
            const response = await axios.get(`${BASE_URL}${asset.path}`);
            console.log(`✅ ${asset.name}: ${response.data.length} bytes`);
        } catch (error) {
            console.log(`❌ ${asset.name}: Failed to load`);
        }
    }
}

// Test API endpoints
async function testAPIEndpoints() {
    console.log('\n🔌 Testing API Endpoints...\n');

    const endpoints = [
        { name: 'Health Check', method: 'GET', path: '/api/health' },
        { name: 'Info', method: 'GET', path: '/api/info' },
        { name: 'Background Styles', method: 'GET', path: '/api/process/styles' },
        { name: 'Status List', method: 'GET', path: '/api/status' }
    ];

    for (const endpoint of endpoints) {
        try {
            const response = await axios({
                method: endpoint.method,
                url: `${BASE_URL}${endpoint.path}`
            });
            console.log(`✅ ${endpoint.name}: ${response.status} ${response.statusText}`);
        } catch (error) {
            console.log(`❌ ${endpoint.name}: ${error.response?.status || 'Failed'}`);
        }
    }
}

// Main test function
async function runAllTests() {
    console.log('🎨 Runware Product Studio - Simple Test Suite');
    console.log('==============================================\n');

    await testFrontendAssets();
    await testAPIEndpoints();
    await testBasicFlow();

    console.log('\n==============================================');
    console.log('🏁 All tests completed!');
    console.log('\n🔧 Manual Testing:');
    console.log('   1. Open http://localhost:3000');
    console.log('   2. Click "Image URL" tab');
    console.log('   3. Enter: ' + TEST_IMAGE_URL);
    console.log('   4. Click "Load" button');
    console.log('   5. Click "Continue to Settings"');
    console.log('   6. Enter your API key');
    console.log('   7. Click "Start Processing"');
}

// Run tests
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ Test suite failed:', error.message);
        process.exit(1);
    });
}

module.exports = { testBasicFlow, testFrontendAssets, testAPIEndpoints };
