const express = require('express');
const { jobs } = require('./process');

const router = express.Router();

// GET /api/status/:jobId - Get job status
router.get('/:jobId', (req, res) => {
  try {
    const jobId = req.params.jobId;
    const job = jobs.get(jobId);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found',
        jobId,
        message: 'Job may have expired or never existed'
      });
    }

    // Calculate progress percentage
    let progressPercentage = 0;
    if (job.status === 'completed') {
      progressPercentage = 100;
    } else if (job.status === 'processing') {
      if (job.type === 'batch' && job.progress) {
        progressPercentage = Math.round((job.progress.completed / job.progress.total) * 100);
      } else {
        // Estimate progress for single job based on time
        const elapsed = Date.now() - job.startTime;
        const estimatedTotal = 45000; // 45 seconds estimate
        progressPercentage = Math.min(90, Math.round((elapsed / estimatedTotal) * 100));
      }
    } else if (job.status === 'failed') {
      progressPercentage = job.progress?.completed ? 
        Math.round((job.progress.completed / job.progress.total) * 100) : 0;
    }

    const response = {
      jobId: job.id,
      status: job.status,
      progress: {
        percentage: progressPercentage,
        current: job.progress?.current || 0,
        total: job.progress?.total || 1,
        completed: job.progress?.completed || 0
      },
      currentStep: job.currentStep || 'Initializing',
      startTime: job.startTime,
      elapsedTime: Date.now() - job.startTime,
      elapsedTimeFormatted: `${Math.round((Date.now() - job.startTime) / 1000)}s`
    };

    // Add type-specific info
    if (job.type === 'batch') {
      response.type = 'batch';
      response.batchInfo = {
        totalImages: job.input.images.length,
        currentImage: job.progress?.current || 0,
        completedImages: job.progress?.completed || 0
      };
    } else {
      response.type = 'single';
    }

    // Add results if completed
    if (job.status === 'completed' && job.result) {
      response.result = job.result;
      response.processingTime = job.processingTime;
      response.processingTimeFormatted = `${(job.processingTime / 1000).toFixed(2)}s`;
    }

    // Add error if failed
    if (job.status === 'failed') {
      response.error = job.error;
      response.processingTime = job.processingTime;
      response.processingTimeFormatted = job.processingTime ? 
        `${(job.processingTime / 1000).toFixed(2)}s` : 'N/A';
    }

    res.json(response);

  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({
      error: 'Failed to get job status',
      message: error.message
    });
  }
});

// GET /api/status - Get all jobs status (for debugging)
router.get('/', (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const status = req.query.status; // filter by status
    
    const allJobs = Array.from(jobs.values())
      .filter(job => !status || job.status === status)
      .sort((a, b) => b.startTime - a.startTime)
      .slice(0, limit)
      .map(job => ({
        id: job.id,
        type: job.type || 'single',
        status: job.status,
        startTime: job.startTime,
        elapsedTime: Date.now() - job.startTime,
        progress: job.progress,
        currentStep: job.currentStep,
        hasResult: !!job.result,
        hasError: !!job.error
      }));

    const summary = {
      total: jobs.size,
      processing: Array.from(jobs.values()).filter(j => j.status === 'processing').length,
      completed: Array.from(jobs.values()).filter(j => j.status === 'completed').length,
      failed: Array.from(jobs.values()).filter(j => j.status === 'failed').length
    };

    res.json({
      summary,
      jobs: allJobs,
      totalShown: allJobs.length
    });

  } catch (error) {
    console.error('Jobs list error:', error);
    res.status(500).json({
      error: 'Failed to get jobs list',
      message: error.message
    });
  }
});

// DELETE /api/status/:jobId - Cancel/delete job
router.delete('/:jobId', (req, res) => {
  try {
    const jobId = req.params.jobId;
    const job = jobs.get(jobId);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found',
        jobId
      });
    }

    // If job is still processing, mark as cancelled
    if (job.status === 'processing') {
      job.status = 'cancelled';
      job.endTime = Date.now();
      job.processingTime = job.endTime - job.startTime;
    }

    // Remove job from memory
    jobs.delete(jobId);

    res.json({
      success: true,
      message: 'Job cancelled and removed',
      jobId
    });

  } catch (error) {
    console.error('Job cancellation error:', error);
    res.status(500).json({
      error: 'Failed to cancel job',
      message: error.message
    });
  }
});

// POST /api/status/cleanup - Manual cleanup of old jobs
router.post('/cleanup', (req, res) => {
  try {
    const maxAge = parseInt(req.body.maxAge) || 2 * 60 * 60 * 1000; // 2 hours default
    const cutoff = Date.now() - maxAge;
    const statusFilter = req.body.status; // optional status filter
    
    let deletedCount = 0;
    
    for (const [jobId, job] of jobs.entries()) {
      const shouldDelete = job.startTime < cutoff && 
        (!statusFilter || job.status === statusFilter);
        
      if (shouldDelete) {
        jobs.delete(jobId);
        deletedCount++;
      }
    }

    res.json({
      success: true,
      message: 'Cleanup completed',
      deletedJobs: deletedCount,
      remainingJobs: jobs.size,
      maxAge: `${Math.round(maxAge / 60000)} minutes`
    });

  } catch (error) {
    console.error('Cleanup error:', error);
    res.status(500).json({
      error: 'Cleanup failed',
      message: error.message
    });
  }
});

// GET /api/status/:jobId/download - Download results as ZIP
router.get('/:jobId/download', async (req, res) => {
  try {
    const jobId = req.params.jobId;
    const job = jobs.get(jobId);

    if (!job || job.status !== 'completed' || !job.result) {
      return res.status(404).json({
        error: 'No results available for download',
        jobId
      });
    }

    // In a production app, you'd create a ZIP file with all the result images
    // For now, we'll return the URLs
    const downloadUrls = [];
    
    if (job.result.results) {
      if (job.result.results.final) {
        downloadUrls.push(...job.result.results.final);
      }
      if (job.result.results.cutout) {
        downloadUrls.push(job.result.results.cutout);
      }
      if (job.result.results.enhanced) {
        downloadUrls.push(job.result.results.enhanced);
      }
    }

    // For batch jobs
    if (job.result.results && Array.isArray(job.result.results)) {
      job.result.results.forEach(result => {
        if (result.results && result.results.final) {
          downloadUrls.push(...result.results.final);
        }
      });
    }

    res.json({
      success: true,
      jobId,
      downloadUrls: [...new Set(downloadUrls)], // Remove duplicates
      totalImages: downloadUrls.length,
      message: 'Use these URLs to download individual images. ZIP download feature coming soon.'
    });

  } catch (error) {
    console.error('Download preparation error:', error);
    res.status(500).json({
      error: 'Failed to prepare download',
      message: error.message
    });
  }
});

module.exports = router;