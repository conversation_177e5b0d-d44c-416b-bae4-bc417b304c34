# 🧪 Manual Test Guide - Runware Product Studio

## Hướng Dẫn Test Thủ Công Website

### 🚀 Bước 1: Khởi Động
1. **Mở terminal và chạy server:**
   ```bash
   npm start
   ```
2. **Mở browser và truy cập:** http://localhost:3000
3. **Mở Developer Tools (F12)** để theo dõi errors

### 📋 Checklist Test Các Button

#### ✅ Test Header Buttons
- [ ] **Help Button**: Click "Help" button ở header
  - Modal help có mở không?
  - Có thể đóng modal bằng X không?
  - Có thể đóng modal bằng click outside không?
  - Có thể đóng modal bằng ESC không?

- [ ] **Settings Button**: Click "Settings" button ở header
  - Modal settings có mở không?
  - Có thể đóng modal bằng X không?
  - Toggle switches có hoạt động không?
  - Save button có hoạt động không?

#### ✅ Test Upload Section (Step 1)
- [ ] **Method Tabs**: 
  - Click "Upload File" tab
  - Click "Image URL" tab
  - Tabs có switch đúng không?

- [ ] **File Upload**:
  - Click vào upload area
  - Drag & drop file (nếu có)
  - File input có mở không?

- [ ] **URL Input**:
  - Nhập URL vào input field
  - Click "Load" button
  - Preview có hiện không?

- [ ] **Example URLs**:
  - Click "Nike Shoes" button
  - Click "Watch" button
  - URL có load vào input không?

- [ ] **Preview Area**:
  - Preview có hiện sau khi load image không?
  - "Clear" button có hoạt động không?
  - "Continue to Settings" button có enable không?

#### ✅ Test Settings Section (Step 2)
- [ ] **API Key Input**:
  - Nhập API key
  - Click eye icon để toggle visibility
  - Type có đổi từ password sang text không?

- [ ] **Background Styles**:
  - Click các style options khác nhau
  - Style có được select không?
  - Visual feedback có đúng không?

- [ ] **Advanced Options**:
  - Thay đổi "Image Enhancement" dropdown
  - Thay đổi "Background Variants" dropdown
  - Thay đổi "Background Method" dropdown
  - Toggle "Final Polish" checkbox

- [ ] **Navigation**:
  - Click "Back" button
  - Click "Start Processing" button

#### ✅ Test Processing Section (Step 3)
- [ ] **Processing UI**:
  - Progress bar có hiện không?
  - Processing steps có hiện không?
  - Cost tracker có hiện không?

- [ ] **Cancel Button**:
  - "Cancel" button có hoạt động không?

#### ✅ Test Results Section (Step 4)
- [ ] **Results Display**:
  - Results gallery có hiện không?
  - Download buttons có hoạt động không?

- [ ] **New Project**:
  - "New Project" button có reset về step 1 không?

### 🔧 Test Scenarios

#### Scenario 1: Complete Flow với URL
1. Mở website
2. Click "Image URL" tab
3. Click "Nike Shoes" example
4. Click "Continue to Settings"
5. Nhập API key: `test_key`
6. Chọn background style khác
7. Thay đổi advanced options
8. Click "Start Processing"
9. Kiểm tra processing screen
10. Click "Cancel" nếu cần

#### Scenario 2: Modal Testing
1. Click "Help" button
2. Đóng modal bằng X
3. Click "Settings" button
4. Toggle các switches
5. Click "Save"
6. Kiểm tra modal đóng

#### Scenario 3: Navigation Testing
1. Load một image
2. Go to settings
3. Click "Back"
4. Kiểm tra về upload screen
5. Go to settings lại
6. Try start processing

### 🐛 Common Issues to Check

#### JavaScript Errors
- Mở Console tab trong DevTools
- Kiểm tra có error messages không
- Đặc biệt chú ý:
  - `showHelp is not defined`
  - `showSettings is not defined`
  - `openModal is not defined`

#### CSS Issues
- Font Awesome icons có load không?
- Buttons có styling đúng không?
- Modals có hiện đúng position không?
- Responsive design có hoạt động không?

#### Network Issues
- Kiểm tra Network tab
- Tất cả resources có load thành công không?
- API calls có response đúng không?

### ✅ Expected Results

#### Working Features:
- ✅ All buttons clickable
- ✅ Modals open/close properly
- ✅ Form inputs work
- ✅ Navigation between steps
- ✅ Image loading from URL
- ✅ Settings persistence
- ✅ Responsive design

#### Known Limitations:
- ⚠️ Font Awesome may not load due to CSP
- ⚠️ Processing requires real API key
- ⚠️ File upload requires server setup

### 🎯 Success Criteria

**All buttons should:**
1. Be clickable (cursor changes to pointer)
2. Provide visual feedback (hover effects)
3. Execute their intended function
4. Not cause JavaScript errors
5. Update UI state appropriately

**Modals should:**
1. Open when triggered
2. Close with X button
3. Close when clicking outside
4. Close with ESC key
5. Not cause scroll issues

**Forms should:**
1. Accept input
2. Validate data
3. Provide feedback
4. Save settings
5. Load saved settings

### 📊 Test Results Template

```
Date: ___________
Tester: _________
Browser: ________

Header Buttons:
- Help Button: ✅/❌
- Settings Button: ✅/❌

Upload Section:
- Method Tabs: ✅/❌
- File Upload: ✅/❌
- URL Input: ✅/❌
- Example URLs: ✅/❌
- Preview: ✅/❌

Settings Section:
- API Key: ✅/❌
- Background Styles: ✅/❌
- Advanced Options: ✅/❌
- Navigation: ✅/❌

Processing Section:
- UI Display: ✅/❌
- Cancel Button: ✅/❌

Overall Score: ___/10
Issues Found: ___________
```

### 🚀 Quick Test Commands

Để test nhanh, mở browser console và chạy:

```javascript
// Test modal functions
showHelp();
closeModal('helpModal');
showSettings();
closeModal('settingsModal');

// Test navigation
goToStep(2);
goToStep(1);

// Test API key toggle
toggleApiKeyVisibility();

// Test example URL
loadExampleUrl('https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500');
```

### 🎉 Kết Luận

Nếu tất cả tests pass, website đã sẵn sàng để sử dụng! 
Nếu có issues, check console errors và network tab để debug.
