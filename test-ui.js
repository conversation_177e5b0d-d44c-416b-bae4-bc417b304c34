#!/usr/bin/env node

/**
 * UI Test Script for Runware Product Studio
 * Tests all UI interactions and button functionality
 */

const puppeteer = require('puppeteer');

const BASE_URL = 'http://localhost:3000';
const TEST_API_KEY = 'test_key_for_ui_testing';
const TEST_IMAGE_URL = 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500';

async function testUI() {
    console.log('🎭 Starting UI Test Suite...\n');

    let browser;
    try {
        // Launch browser
        browser = await puppeteer.launch({ 
            headless: false, // Set to true for headless testing
            defaultViewport: { width: 1280, height: 720 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();
        
        // Enable console logging
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('❌ Browser Error:', msg.text());
            }
        });

        // Navigate to the app
        console.log('1️⃣ Loading application...');
        await page.goto(BASE_URL, { waitUntil: 'networkidle0' });
        console.log('✅ Application loaded');

        // Test 1: Header buttons
        console.log('\n2️⃣ Testing header buttons...');
        
        // Test Help button
        await page.click('button[onclick="showHelp()"]');
        await page.waitForSelector('#helpModal.active', { timeout: 2000 });
        console.log('✅ Help modal opens');
        
        // Close help modal
        await page.click('#helpModal .modal-close');
        await page.waitForFunction(() => !document.querySelector('#helpModal.active'), { timeout: 2000 });
        console.log('✅ Help modal closes');

        // Test Settings button
        await page.click('button[onclick="showSettings()"]');
        await page.waitForSelector('#settingsModal.active', { timeout: 2000 });
        console.log('✅ Settings modal opens');
        
        // Close settings modal
        await page.click('#settingsModal .modal-close');
        await page.waitForFunction(() => !document.querySelector('#settingsModal.active'), { timeout: 2000 });
        console.log('✅ Settings modal closes');

        // Test 2: Upload methods
        console.log('\n3️⃣ Testing upload methods...');
        
        // Test URL tab
        await page.click('.method-tab[data-method="url"]');
        await page.waitForSelector('#upload-url.active', { timeout: 1000 });
        console.log('✅ URL tab switches correctly');

        // Test file tab
        await page.click('.method-tab[data-method="file"]');
        await page.waitForSelector('#upload-file.active', { timeout: 1000 });
        console.log('✅ File tab switches correctly');

        // Test 3: URL loading
        console.log('\n4️⃣ Testing URL loading...');
        
        // Switch to URL tab and enter URL
        await page.click('.method-tab[data-method="url"]');
        await page.type('#imageUrl', TEST_IMAGE_URL);
        await page.click('button[onclick="loadFromUrl()"]');
        
        // Wait for preview to appear
        await page.waitForSelector('#preview-area[style*="block"]', { timeout: 5000 });
        console.log('✅ Image loads from URL');
        console.log('✅ Preview area appears');

        // Check if Continue button is enabled
        const continueButton = await page.$('#nextToSettings:not([disabled])');
        if (continueButton) {
            console.log('✅ Continue button enabled after image load');
        }

        // Test 4: Navigation to settings
        console.log('\n5️⃣ Testing navigation...');
        
        await page.click('#nextToSettings');
        await page.waitForSelector('#settings-section.active', { timeout: 2000 });
        console.log('✅ Navigation to settings works');

        // Test 5: Settings form
        console.log('\n6️⃣ Testing settings form...');
        
        // Enter API key
        await page.type('#apiKey', TEST_API_KEY);
        console.log('✅ API key input works');

        // Test API key visibility toggle
        await page.click('button[onclick="toggleApiKeyVisibility()"]');
        const apiKeyType = await page.$eval('#apiKey', el => el.type);
        if (apiKeyType === 'text') {
            console.log('✅ API key visibility toggle works');
        }

        // Test background style selection
        const styleOptions = await page.$$('.style-option');
        if (styleOptions.length > 0) {
            await styleOptions[1].click(); // Click second style
            console.log('✅ Background style selection works');
        }

        // Test advanced options
        await page.select('#upscaleFactor', '3');
        await page.select('#generateCount', '6');
        await page.select('#backgroundMethod', 'composition');
        console.log('✅ Advanced options work');

        // Test final polish toggle
        await page.click('#finalPolish');
        console.log('✅ Final polish toggle works');

        // Test 6: Back navigation
        console.log('\n7️⃣ Testing back navigation...');
        
        await page.click('button[onclick="goToStep(1)"]');
        await page.waitForSelector('#upload-section.active', { timeout: 2000 });
        console.log('✅ Back navigation works');

        // Go back to settings
        await page.click('#nextToSettings');
        await page.waitForSelector('#settings-section.active', { timeout: 2000 });

        // Test 7: Start processing (will fail without real API key)
        console.log('\n8️⃣ Testing processing start...');
        
        try {
            await page.click('button[onclick="startProcessing()"]');
            await page.waitForSelector('#processing-section.active', { timeout: 2000 });
            console.log('✅ Processing section loads');
            
            // Check if cancel button appears
            const cancelButton = await page.$('#cancelBtn');
            if (cancelButton) {
                console.log('✅ Cancel button appears');
            }
            
        } catch (error) {
            console.log('⚠️ Processing start failed (expected without real API key)');
        }

        // Test 8: Example URL buttons
        console.log('\n9️⃣ Testing example URL buttons...');
        
        // Go back to upload
        await page.goto(BASE_URL);
        await page.click('.method-tab[data-method="url"]');
        
        // Test example URL buttons
        const exampleButtons = await page.$$('.example-url');
        if (exampleButtons.length > 0) {
            await exampleButtons[0].click();
            await page.waitForTimeout(2000); // Wait for image to load
            console.log('✅ Example URL button works');
        }

        console.log('\n🎉 All UI tests completed successfully!');
        
        console.log('\n📋 Test Summary:');
        console.log('   ✅ Header buttons: OK');
        console.log('   ✅ Modal functionality: OK');
        console.log('   ✅ Upload methods: OK');
        console.log('   ✅ URL loading: OK');
        console.log('   ✅ Navigation: OK');
        console.log('   ✅ Settings form: OK');
        console.log('   ✅ Advanced options: OK');
        console.log('   ✅ Example URLs: OK');

    } catch (error) {
        console.error('❌ UI Test failed:', error.message);
        throw error;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Error handling
process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled error:', error.message);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    testUI().catch(error => {
        console.error('❌ Test suite failed:', error.message);
        process.exit(1);
    });
}

module.exports = { testUI };
