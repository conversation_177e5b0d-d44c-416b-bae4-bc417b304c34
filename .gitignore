# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Application specific files
backend/uploads/input/*
backend/uploads/output/*
!backend/uploads/input/.gitkeep
!backend/uploads/output/.gitkeep

# Database files
*.db
*.sqlite
*.sqlite3
data/

# Session files
sessions/

# Cache files
.cache/
cache/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor and IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*.old

# Test coverage
coverage/
test-results/

# Production build files
build/
dist/

# PM2 files
ecosystem.config.js
.pm2/

# Docker files (if not using)
Dockerfile
docker-compose.yml
.dockerignore

# Security and sensitive files
*.pem
*.key
*.crt
config/production.json
config/secrets.json

# Large files that shouldn't be in git
*.zip
*.tar.gz
*.rar
*.7z

# Specific to this project
# Processed images (too large for git)
results/
processed/
examples/large/

# Configuration backups
config/*.backup
config/*.bak

# User uploaded content
uploads/
user-content/

# Generated documentation
docs/generated/