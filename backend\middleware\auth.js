/**
 * Authentication middleware for API key validation
 */

const validateApiKey = (req, res, next) => {
  const apiKey = req.body.apiKey || req.headers['x-api-key'] || process.env.RUNWARE_API_KEY;
  
  if (!apiKey) {
    return res.status(401).json({
      error: 'API key required',
      message: 'Please provide your Runware API key in request body or headers'
    });
  }
  
  // Basic API key format validation
  if (typeof apiKey !== 'string' || apiKey.length < 10) {
    return res.status(401).json({
      error: 'Invalid API key format',
      message: 'API key must be a valid string'
    });
  }
  
  // Store API key for later use
  req.runwareApiKey = apiKey;
  next();
};

/**
 * Optional API key validation (doesn't fail if missing)
 */
const optionalApiKey = (req, res, next) => {
  const apiKey = req.body.apiKey || req.headers['x-api-key'] || process.env.RUNWARE_API_KEY;
  
  if (apiKey) {
    req.runwareApiKey = apiKey;
  }
  
  next();
};

module.exports = {
  validateApiKey,
  optionalApiKey
};