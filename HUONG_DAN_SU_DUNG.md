# 🎨 Hướng Dẫn Sử Dụng Runware Product Studio

## 📋 Tổng Quan

Runware Product Studio là công cụ AI mạnh mẽ giúp bạn tạo ra những hình ảnh sản phẩm chuyên nghiệp với quy trình hoàn toàn tự động:

1. **Remove Background** - Loại bỏ background tự động
2. **Upscale Image** - Tăng chất lượng hình ảnh 2x-4x  
3. **Generate Background** - Tạo background mới với 8 style khác nhau

## 🚀 Bắt Đầu Nhanh

### Bước 1: Cài Đặt
```bash
# Clone repository
git clone <repository-url>
cd runware-product-studio

# Cài đặt dependencies
npm install

# Tạo file cấu hình
cp .env.example .env
```

### Bước 2: Cấu Hình API Key
1. Truy cập [runware.ai](https://runware.ai) để đăng ký tài khoản
2. Lấy API key từ dashboard
3. Thêm vào file `.env`:
```env
RUNWARE_API_KEY=your_api_key_here
```

### Bước 3: Chạy Ứng Dụng
```bash
npm start
```
Mở trình duyệt tại: http://localhost:3000

## 📖 Hướng Dẫn Chi Tiết

### 🖼️ Bước 1: Upload Hình Ảnh

**Phương pháp 1: Upload File**
- Kéo thả file vào vùng upload
- Hoặc click để chọn file từ máy tính
- Hỗ trợ: JPG, PNG, WEBP (tối đa 10MB)

**Phương pháp 2: Nhập URL**
- Dán link hình ảnh trực tiếp
- Ví dụ: https://example.com/product.jpg
- Có sẵn một số ví dụ để test

### ⚙️ Bước 2: Cấu Hình Settings

**API Key**
- Nhập Runware API key của bạn
- Có thể lưu vào browser để sử dụng lần sau

**Background Style (8 lựa chọn):**
- 🤍 **Minimalist**: Background trắng sạch, tối giản
- ✨ **Luxury**: Bề mặt marble cao cấp, sang trọng  
- 🌿 **Natural**: Gỗ tự nhiên, ánh sáng ấm áp
- 🏢 **Modern**: Bê tông hiện đại, phong cách công nghiệp
- 🏠 **Lifestyle**: Không gian gia đình ấm cúng
- 🌅 **Outdoor**: Môi trường ngoài trời tự nhiên
- 📸 **Studio**: Studio chuyên nghiệp
- 🎨 **Abstract**: Hình học trừu tượng, sáng tạo

**Tùy Chọn Nâng Cao:**
- **Image Enhancement**: Chọn mức độ upscale (1x-4x)
  - 2x: Khuyến nghị cho hầu hết sản phẩm
  - 3x-4x: Cho sản phẩm cao cấp (chậm hơn, đắt hơn)

- **Background Variants**: Số lượng ảnh tạo ra (2-8)
  - 4 ảnh: Khuyến nghị cho đa dạng
  - 6-8 ảnh: Cho nhiều lựa chọn hơn

- **Background Method**: Phương pháp tạo background
  - 🎯 **Inpainting**: Tích hợp tốt hơn với sản phẩm
  - 🎨 **Composition**: Sáng tạo hơn, background riêng biệt

- **Final Polish**: Cải thiện thêm cho 2 ảnh tốt nhất
  - Bật cho sản phẩm cao cấp
  - Tăng chi phí và thời gian xử lý

### 🤖 Bước 3: Xử Lý AI

**Theo Dõi Tiến Trình:**
- Thanh progress bar hiển thị % hoàn thành
- 3 bước xử lý được hiển thị rõ ràng:
  1. Background Removal (Loại bỏ background)
  2. Image Enhancement (Cải thiện chất lượng)  
  3. Background Generation (Tạo background mới)

**Thông Tin Chi Phí:**
- Ước tính chi phí: $0.02 - $0.05 mỗi sản phẩm
- Thời gian xử lý: 30-60 giây
- Có thể hủy bất cứ lúc nào

### 📥 Bước 4: Tải Kết Quả

**Xem Kết Quả:**
- Gallery hiển thị tất cả ảnh đã tạo
- Bao gồm: Product cutout, Enhanced version, Background variants
- Click "View" để xem full size

**Tải Xuống:**
- "Download": Tải từng ảnh riêng lẻ
- "Download All": Tải tất cả cùng lúc
- Format: JPG chất lượng cao

## 💡 Mẹo Sử Dụng

### Để Có Kết Quả Tốt Nhất:
1. **Hình ảnh đầu vào:**
   - Sản phẩm rõ nét, không bị mờ
   - Background đơn giản (trắng/đơn sắc tốt nhất)
   - Ánh sáng đều, không quá tối/sáng

2. **Cài đặt khuyến nghị:**
   - Upscale 2x cho hầu hết sản phẩm
   - 4 background variants cho đa dạng
   - Inpainting method cho tích hợp tự nhiên

3. **Tối ưu chi phí:**
   - Batch processing cho nhiều sản phẩm
   - Chỉ dùng Final Polish cho sản phẩm cao cấp
   - Test với ít variants trước khi scale up

### Xử Lý Lỗi Thường Gặp:

**Lỗi API Key:**
- Kiểm tra API key có đúng không
- Đảm bảo tài khoản có đủ credits
- Thử tạo API key mới

**Upload thất bại:**
- File quá lớn (>10MB): Nén ảnh trước khi upload
- Format không hỗ trợ: Chuyển sang JPG/PNG/WEBP
- Kết nối mạng không ổn định: Thử lại

**Xử lý bị stuck:**
- Kiểm tra kết nối internet
- Refresh trang và thử lại
- Dùng ảnh nhỏ hơn để test

## 🔧 Cấu Hình Nâng Cao

### File .env:
```env
# API Configuration
RUNWARE_API_KEY=your_key_here
PORT=3000

# Upload Settings  
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,webp

# Processing Settings
DEFAULT_UPSCALE_FACTOR=2
MAX_BACKGROUND_VARIANTS=8
DEFAULT_OUTPUT_QUALITY=95

# Model Settings (Advanced)
DEFAULT_BACKGROUND_REMOVAL_MODEL=runware:112@5
DEFAULT_IMAGE_GENERATION_MODEL=civitai:4201@130090
```

### Tùy Chỉnh Models:
- Background removal: Có thể thay đổi model để có kết quả khác nhau
- Image generation: Thử các model khác nhau cho style riêng
- Upscaling: Điều chỉnh quality vs speed

## 📊 Chi Phí Chi Tiết

### Breakdown Chi Phí (mỗi ảnh):
- Background Removal: ~$0.006
- Image Upscaling (2x): ~$0.002  
- Background Generation (4 variants): ~$0.016-0.032
- Final Polish (optional): ~$0.004
- **Tổng cộng**: ~$0.024-0.044 mỗi sản phẩm

### So Sánh Phương Pháp:
- **Inpainting**: Chi phí cao hơn ~20% nhưng tích hợp tốt hơn
- **Composition**: Rẻ hơn, sáng tạo hơn nhưng có thể kém tự nhiên

## 🎯 Use Cases Thực Tế

### E-commerce:
- Tạo ảnh sản phẩm cho website bán hàng
- Đồng nhất style cho toàn bộ catalog
- A/B test các background khác nhau

### Marketing:
- Social media posts
- Quảng cáo Facebook/Google
- Email marketing campaigns

### Photography Studio:
- Xử lý hàng loạt ảnh sản phẩm
- Tạo variations cho client lựa chọn
- Tiết kiệm thời gian setup studio

## 🚀 Kết Luận

Runware Product Studio giúp bạn tạo ra hình ảnh sản phẩm chuyên nghiệp chỉ trong vài phút với chi phí thấp. Công cụ hoàn hảo cho:

- 🛒 **E-commerce sellers** cần ảnh sản phẩm đẹp
- 📸 **Photographers** muốn tăng năng suất  
- 🎨 **Marketers** cần content visual nhanh chóng
- 🏢 **Businesses** muốn chuẩn hóa hình ảnh

**Bắt đầu ngay hôm nay và biến đổi cách bạn tạo ảnh sản phẩm! 🎉**
