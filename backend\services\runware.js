const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs').promises;
const path = require('path');

class RunwareService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.runware.ai/v1';
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 60000 // 60 seconds timeout
    });
  }

  async apiCall(payload, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔄 API call attempt ${attempt}/${retries}:`, payload[0].taskType);

        const response = await this.client.post('', payload);

        if (!response.data || !response.data.data) {
          throw new Error('Invalid API response format');
        }

        return response.data;
      } catch (error) {
        console.error(`❌ API call attempt ${attempt} failed:`, error.message);

        if (attempt === retries) {
          throw new Error(`API call failed after ${retries} attempts: ${error.message}`);
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  // Convert file to base64 data URI
  async fileToDataUri(filePath) {
    try {
      const buffer = await fs.readFile(filePath);
      const ext = path.extname(filePath).toLowerCase().slice(1);
      const mimeType = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'webp': 'image/webp'
      }[ext] || 'image/jpeg';

      return `data:${mimeType};base64,${buffer.toString('base64')}`;
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  // Step 1: Remove background
  async removeBackground(imageInput, model = "runware:112@5") {
    let inputImage;

    // Handle different input types
    if (imageInput.startsWith('http')) {
      inputImage = imageInput; // URL
    } else if (imageInput.startsWith('data:')) {
      inputImage = imageInput; // Data URI
    } else {
      // File path - convert to data URI
      inputImage = await this.fileToDataUri(imageInput);
    }

    const payload = [{
      "taskType": "imageBackgroundRemoval",
      "taskUUID": uuidv4(),
      "inputImage": inputImage,
      "outputType": "URL",
      "outputFormat": "PNG",
      "model": model,
      "settings": {
        "rgba": [255, 255, 255, 0],
        "postProcessMask": true,
        "returnOnlyMask": false,
        "alphaMatting": true,
        "alphaMattingForegroundThreshold": 240,
        "alphaMattingBackgroundThreshold": 10,
        "alphaMattingErodeSize": 10
      },
      "includeCost": true
    }];

    const result = await this.apiCall(payload);
    return {
      url: result.data[0].imageURL,
      uuid: result.data[0].imageUUID,
      cost: result.data[0].cost || 0
    };
  }

  // Step 2: Enhance/Upscale image
  async enhanceImage(imageUrl, factor = 2) {
    const payload = [{
      "taskType": "imageUpscale",
      "taskUUID": uuidv4(),
      "inputImage": imageUrl,
      "outputType": "URL",
      "outputFormat": "PNG",
      "outputQuality": parseInt(process.env.DEFAULT_OUTPUT_QUALITY) || 95,
      "upscaleFactor": factor,
      "includeCost": true
    }];

    const result = await this.apiCall(payload);
    return {
      url: result.data[0].imageURL,
      uuid: result.data[0].imageUUID,
      cost: result.data[0].cost || 0
    };
  }

  // Step 3: Generate backgrounds using inpainting technique
  async generateBackgrounds(productUrl, backgroundStyle, count = 4) {
    const styles = {
      minimalist: {
        prompt: "clean white background, minimalist studio, soft shadows, product photography, professional lighting, seamless backdrop",
        negativePrompt: "cluttered, busy, distracting elements, dark shadows, harsh lighting"
      },
      luxury: {
        prompt: "luxury marble surface, elegant lighting, premium feel, high-end photography, sophisticated ambiance, gold accents, velvet texture",
        negativePrompt: "cheap, plastic, low quality, harsh lighting, cluttered"
      },
      natural: {
        prompt: "natural wooden table, soft natural lighting, organic feel, lifestyle photography, warm tones, plants, natural textures",
        negativePrompt: "artificial, plastic, harsh lighting, cold tones, industrial"
      },
      modern: {
        prompt: "modern concrete surface, contemporary lighting, industrial feel, architectural photography, clean lines, geometric shapes",
        negativePrompt: "old fashioned, cluttered, soft textures, warm colors"
      },
      lifestyle: {
        prompt: "cozy home setting, warm lighting, lifestyle photography, lived-in feel, comfortable atmosphere, soft textures",
        negativePrompt: "sterile, cold, professional studio, harsh lighting"
      },
      outdoor: {
        prompt: "natural outdoor setting, soft daylight, fresh environment, natural photography, scenic background, bokeh effect",
        negativePrompt: "indoor, artificial lighting, studio setup, harsh shadows"
      },
      studio: {
        prompt: "professional photography studio, softbox lighting, seamless backdrop, commercial photography, gradient background",
        negativePrompt: "amateur, harsh shadows, cluttered, distracting elements"
      },
      abstract: {
        prompt: "abstract geometric background, modern design, artistic composition, creative photography, gradient colors, smooth transitions",
        negativePrompt: "realistic, photographic, cluttered, busy patterns"
      }
    };

    const styleConfig = styles[backgroundStyle] || styles.minimalist;

    // Create a mask for the background area (inverse of the product)
    const maskPayload = [{
      "taskType": "imageBackgroundRemoval",
      "taskUUID": uuidv4(),
      "inputImage": productUrl,
      "outputType": "URL",
      "outputFormat": "PNG",
      "model": "runware:112@5", // BiRefNet General
      "settings": {
        "returnOnlyMask": true,
        "postProcessMask": true
      },
      "includeCost": true
    }];

    const maskResult = await this.apiCall(maskPayload);
    const maskUrl = maskResult.data[0].imageURL;

    // Generate new backgrounds using inpainting
    const payload = [{
      "taskType": "imageInference",
      "taskUUID": uuidv4(),
      "positivePrompt": `${styleConfig.prompt}, studio lighting, high resolution, commercial photography, clean composition, professional, 8k, detailed, sharp focus, perfect lighting`,
      "negativePrompt": `${styleConfig.negativePrompt}, blurry, low quality, distorted, overexposed, underexposed, noisy, artifact, watermark, text, logo, cropped, out of frame, deformed, mutation, extra limbs`,
      "seedImage": productUrl,
      "maskImage": maskUrl,
      "strength": 0.85, // Higher strength for background replacement
      "model": "civitai:4201@130090", // SDXL model for better quality
      "height": 1024,
      "width": 1024,
      "steps": 35,
      "CFGScale": 8.0,
      "numberResults": Math.min(count, parseInt(process.env.MAX_BACKGROUND_VARIANTS) || 6),
      "scheduler": "DPM++ 2M Karras",
      "outputType": "URL",
      "outputFormat": "JPG",
      "outputQuality": parseInt(process.env.DEFAULT_OUTPUT_QUALITY) || 95,
      "includeCost": true
    }];

    const result = await this.apiCall(payload);
    const totalCost = maskResult.data[0].cost + result.data.reduce((sum, item) => sum + (item.cost || 0), 0);

    return {
      images: result.data.map(item => ({
        url: item.imageURL,
        uuid: item.imageUUID,
        cost: item.cost || 0
      })),
      maskUrl: maskUrl,
      totalCost: totalCost
    };
  }

  // Step 4: Create completely new backgrounds (alternative method)
  async createNewBackgrounds(productUrl, backgroundStyle, count = 4) {
    const styles = {
      minimalist: "clean white studio background, minimalist, soft lighting, product photography",
      luxury: "luxury marble background, gold accents, premium lighting, elegant",
      natural: "natural wood background, organic textures, warm lighting",
      modern: "modern concrete background, industrial, clean lines, contemporary",
      lifestyle: "cozy home background, warm atmosphere, lifestyle setting",
      outdoor: "natural outdoor background, soft daylight, scenic environment",
      studio: "professional studio background, gradient lighting, seamless",
      abstract: "abstract geometric background, modern design, artistic"
    };

    const prompt = styles[backgroundStyle] || styles.minimalist;

    const payload = [{
      "taskType": "imageInference",
      "taskUUID": uuidv4(),
      "positivePrompt": `${prompt}, high resolution, commercial photography, professional lighting, 8k, detailed, sharp focus`,
      "negativePrompt": "blurry, low quality, distorted, noisy, artifact, watermark, text, logo, people, faces",
      "model": "civitai:4201@130090", // SDXL model
      "height": 1024,
      "width": 1024,
      "steps": 30,
      "CFGScale": 7.5,
      "numberResults": Math.min(count, parseInt(process.env.MAX_BACKGROUND_VARIANTS) || 6),
      "outputType": "URL",
      "outputFormat": "JPG",
      "outputQuality": parseInt(process.env.DEFAULT_OUTPUT_QUALITY) || 95,
      "includeCost": true
    }];

    const result = await this.apiCall(payload);
    return {
      images: result.data.map(item => ({
        url: item.imageURL,
        uuid: item.imageUUID,
        cost: item.cost || 0
      })),
      totalCost: result.data.reduce((sum, item) => sum + (item.cost || 0), 0)
    };
  }

  // Step 5: Composite product onto new background
  async compositeProductOnBackground(productUrl, backgroundUrl) {
    // This would use image composition techniques
    // For now, we'll use a simple overlay approach with ControlNet
    const payload = [{
      "taskType": "imageInference",
      "taskUUID": uuidv4(),
      "positivePrompt": "professional product photography, perfect composition, studio lighting, commercial quality",
      "negativePrompt": "blurry, distorted, low quality, artifacts",
      "seedImage": backgroundUrl,
      "strength": 0.3,
      "model": "civitai:4201@130090",
      "height": 1024,
      "width": 1024,
      "steps": 25,
      "CFGScale": 7.0,
      "controlNet": [{
        "model": "runware:112@1", // ControlNet for composition
        "guideImage": productUrl,
        "weight": 0.8,
        "startStep": 0,
        "endStep": 15
      }],
      "outputType": "URL",
      "outputFormat": "JPG",
      "outputQuality": parseInt(process.env.DEFAULT_OUTPUT_QUALITY) || 95,
      "includeCost": true
    }];

    const result = await this.apiCall(payload);
    return {
      url: result.data[0].imageURL,
      uuid: result.data[0].imageUUID,
      cost: result.data[0].cost || 0
    };
  }

  // Complete workflow
  async processProduct(imageInput, options = {}) {
    const {
      backgroundStyle = 'minimalist',
      upscaleFactor = parseInt(process.env.DEFAULT_UPSCALE_FACTOR) || 2,
      generateCount = 4,
      finalPolish = false,
      useInpainting = true, // New option to choose background generation method
      verbose = true
    } = options;

    const startTime = Date.now();
    let totalCost = 0;
    const steps = [];

    try {
      // Step 1: Remove background
      if (verbose) console.log('🔄 Step 1: Removing background...');
      const cutout = await this.removeBackground(imageInput);
      totalCost += cutout.cost;
      steps.push({
        step: 1,
        name: 'Background Removal',
        status: 'completed',
        result: cutout.url,
        cost: cutout.cost
      });
      if (verbose) console.log('✅ Background removed:', cutout.url);

      // Step 2: Enhance quality
      if (verbose) console.log('🔍 Step 2: Enhancing image quality...');
      const enhanced = await this.enhanceImage(cutout.url, upscaleFactor);
      totalCost += enhanced.cost;
      steps.push({
        step: 2,
        name: 'Image Enhancement',
        status: 'completed',
        result: enhanced.url,
        cost: enhanced.cost,
        upscaleFactor
      });
      if (verbose) console.log('✅ Image enhanced:', enhanced.url);

      // Step 3: Generate backgrounds
      if (verbose) console.log('🎨 Step 3: Generating backgrounds...');
      let backgrounds;

      if (useInpainting) {
        // Use inpainting method for better integration
        backgrounds = await this.generateBackgrounds(enhanced.url, backgroundStyle, generateCount);
      } else {
        // Use separate background generation + composition
        const newBackgrounds = await this.createNewBackgrounds(enhanced.url, backgroundStyle, generateCount);
        const compositeResults = await Promise.all(
          newBackgrounds.images.map(bg =>
            this.compositeProductOnBackground(enhanced.url, bg.url)
          )
        );

        backgrounds = {
          images: compositeResults,
          totalCost: newBackgrounds.totalCost + compositeResults.reduce((sum, r) => sum + r.cost, 0)
        };
      }

      totalCost += backgrounds.totalCost;
      steps.push({
        step: 3,
        name: 'Background Generation',
        status: 'completed',
        results: backgrounds.images.map(img => img.url),
        cost: backgrounds.totalCost,
        backgroundStyle,
        variantCount: backgrounds.images.length,
        method: useInpainting ? 'inpainting' : 'composition'
      });
      if (verbose) console.log('✅ Backgrounds generated:', backgrounds.images.length, 'variants');

      // Step 4: Final polish (optional)
      let finalImages = backgrounds.images;
      if (finalPolish) {
        if (verbose) console.log('✨ Step 4: Final polishing...');
        const topImages = backgrounds.images.slice(0, 2);
        const polished = await Promise.all(
          topImages.map(img => this.enhanceImage(img.url, 1.5))
        );
        totalCost += polished.reduce((sum, p) => sum + p.cost, 0);

        finalImages = [
          ...polished.map(p => ({ url: p.url, uuid: p.uuid, cost: p.cost })),
          ...backgrounds.images.slice(2)
        ];

        steps.push({
          step: 4,
          name: 'Final Polish',
          status: 'completed',
          results: polished.map(p => p.url),
          cost: polished.reduce((sum, p) => sum + p.cost, 0)
        });
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        jobId: uuidv4(),
        input: {
          type: imageInput.startsWith('http') ? 'url' : 'file',
          source: imageInput
        },
        steps,
        results: {
          cutout: cutout.url,
          enhanced: enhanced.url,
          final: finalImages.map(img => img.url)
        },
        metadata: {
          processingTime: Math.round(processingTime),
          processingTimeFormatted: `${(processingTime / 1000).toFixed(2)}s`,
          totalCost: parseFloat(totalCost.toFixed(6)),
          totalCostFormatted: `$${totalCost.toFixed(4)}`,
          backgroundStyle,
          upscaleFactor,
          variantCount: finalImages.length,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;

      return {
        success: false,
        jobId: uuidv4(),
        error: error.message,
        steps: steps.map(step => ({ ...step, status: 'completed' })), // Mark completed steps
        metadata: {
          processingTime: Math.round(processingTime),
          processingTimeFormatted: `${(processingTime / 1000).toFixed(2)}s`,
          totalCost: parseFloat(totalCost.toFixed(6)),
          totalCostFormatted: `$${totalCost.toFixed(4)}`,
          timestamp: new Date().toISOString(),
          failedAt: steps.length + 1
        }
      };
    }
  }

  // Batch processing
  async processBatch(imageInputs, options = {}) {
    const results = [];
    const batchStartTime = Date.now();

    for (const [index, imageInput] of imageInputs.entries()) {
      console.log(`\n📦 Processing image ${index + 1}/${imageInputs.length}`);

      const result = await this.processProduct(imageInput, {
        ...options,
        verbose: false // Reduce logging for batch
      });

      results.push({
        index,
        imageInput,
        ...result
      });

      // Delay between requests to avoid rate limiting
      if (index < imageInputs.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    const totalTime = Date.now() - batchStartTime;
    const totalCost = results.reduce((sum, r) => sum + (r.metadata?.totalCost || 0), 0);

    return {
      batchId: uuidv4(),
      results,
      summary: {
        total: imageInputs.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        totalProcessingTime: Math.round(totalTime),
        totalCost: parseFloat(totalCost.toFixed(6)),
        averageTimePerImage: Math.round(totalTime / imageInputs.length),
        timestamp: new Date().toISOString()
      }
    };
  }

  // Get available background styles
  static getBackgroundStyles() {
    return {
      minimalist: {
        name: "Minimalist",
        description: "Clean white background with soft shadows",
        preview: "🤍"
      },
      luxury: {
        name: "Luxury",
        description: "Premium marble surface with elegant lighting",
        preview: "✨"
      },
      natural: {
        name: "Natural",
        description: "Wooden surface with warm natural lighting",
        preview: "🌿"
      },
      modern: {
        name: "Modern",
        description: "Contemporary concrete with industrial feel",
        preview: "🏢"
      },
      lifestyle: {
        name: "Lifestyle",
        description: "Cozy home setting with warm atmosphere",
        preview: "🏠"
      },
      outdoor: {
        name: "Outdoor",
        description: "Natural outdoor environment with soft daylight",
        preview: "🌅"
      },
      studio: {
        name: "Studio",
        description: "Professional photography studio setup",
        preview: "📸"
      },
      abstract: {
        name: "Abstract",
        description: "Modern geometric and artistic backgrounds",
        preview: "🎨"
      }
    };
  }
}

module.exports = RunwareService;