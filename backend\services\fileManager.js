const fs = require('fs').promises;
const path = require('path');

class FileManager {
  constructor() {
    this.uploadDir = path.join(__dirname, '../uploads');
    this.inputDir = path.join(this.uploadDir, 'input');
    this.outputDir = path.join(this.uploadDir, 'output');
  }

  // Ensure directories exist
  async ensureDirectories() {
    const dirs = [this.uploadDir, this.inputDir, this.outputDir];
    
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        console.error(`Failed to create directory ${dir}:`, error.message);
      }
    }
  }

  // Get file stats
  async getFileStats(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      };
    } catch (error) {
      return null;
    }
  }

  // Clean old files
  async cleanupOldFiles(maxAgeHours = 24) {
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
    const cutoff = Date.now() - maxAge;
    let cleanedCount = 0;
    let totalSize = 0;

    const cleanDirectory = async (dir) => {
      try {
        const files = await fs.readdir(dir);
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = await this.getFileStats(filePath);
          
          if (stats && stats.isFile && stats.modified.getTime() < cutoff) {
            await fs.unlink(filePath);
            cleanedCount++;
            totalSize += stats.size;
            console.log(`🗑️ Cleaned up old file: ${file}`);
          }
        }
      } catch (error) {
        console.error(`Error cleaning directory ${dir}:`, error.message);
      }
    };

    await cleanDirectory(this.inputDir);
    await cleanDirectory(this.outputDir);

    return {
      cleanedCount,
      totalSize,
      totalSizeFormatted: this.formatFileSize(totalSize)
    };
  }

  // Get directory info
  async getDirectoryInfo(dir) {
    try {
      const files = await fs.readdir(dir);
      let totalSize = 0;
      let fileCount = 0;

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = await this.getFileStats(filePath);
        
        if (stats && stats.isFile) {
          totalSize += stats.size;
          fileCount++;
        }
      }

      return {
        fileCount,
        totalSize,
        totalSizeFormatted: this.formatFileSize(totalSize),
        files: files.filter(async (file) => {
          const stats = await this.getFileStats(path.join(dir, file));
          return stats && stats.isFile;
        })
      };
    } catch (error) {
      return {
        fileCount: 0,
        totalSize: 0,
        totalSizeFormatted: '0 B',
        files: []
      };
    }
  }

  // Format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }

  // Validate file type
  isValidFileType(filename, allowedTypes = ['jpg', 'jpeg', 'png', 'webp']) {
    const ext = path.extname(filename).toLowerCase().slice(1);
    return allowedTypes.includes(ext);
  }

  // Validate file size
  isValidFileSize(size, maxSize = 10 * 1024 * 1024) {
    return size <= maxSize;
  }

  // Generate unique filename
  generateUniqueFilename(originalName) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    return `${name}_${timestamp}_${random}${ext}`;
  }

  // Check if file exists
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  // Move file
  async moveFile(sourcePath, destinationPath) {
    try {
      await fs.rename(sourcePath, destinationPath);
      return true;
    } catch (error) {
      console.error('Move file error:', error);
      return false;
    }
  }

  // Copy file
  async copyFile(sourcePath, destinationPath) {
    try {
      await fs.copyFile(sourcePath, destinationPath);
      return true;
    } catch (error) {
      console.error('Copy file error:', error);
      return false;
    }
  }

  // Delete file
  async deleteFile(filePath) {
    try {
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      console.error('Delete file error:', error);
      return false;
    }
  }

  // Get disk usage
  async getDiskUsage() {
    const inputInfo = await this.getDirectoryInfo(this.inputDir);
    const outputInfo = await this.getDirectoryInfo(this.outputDir);

    return {
      input: inputInfo,
      output: outputInfo,
      total: {
        fileCount: inputInfo.fileCount + outputInfo.fileCount,
        totalSize: inputInfo.totalSize + outputInfo.totalSize,
        totalSizeFormatted: this.formatFileSize(inputInfo.totalSize + outputInfo.totalSize)
      }
    };
  }

  // Auto cleanup scheduler
  startAutoCleanup(intervalMinutes = 60, maxAgeHours = 24) {
    const interval = intervalMinutes * 60 * 1000;
    
    setInterval(async () => {
      console.log('🧹 Running scheduled cleanup...');
      const result = await this.cleanupOldFiles(maxAgeHours);
      
      if (result.cleanedCount > 0) {
        console.log(`✅ Cleanup completed: ${result.cleanedCount} files, ${result.totalSizeFormatted} freed`);
      }
    }, interval);

    console.log(`🕒 Auto cleanup scheduled: every ${intervalMinutes} minutes, files older than ${maxAgeHours} hours`);
  }
}

module.exports = FileManager;