#!/usr/bin/env node

/**
 * Test script for Runware Product Studio API
 * Tests all major endpoints and functionality
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';
const TEST_API_KEY = process.env.RUNWARE_API_KEY || 'test_key';

// Test image URL
const TEST_IMAGE_URL = 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=500';

async function testAPI() {
    console.log('🧪 Testing Runware Product Studio API...\n');

    try {
        // Test 1: Health Check
        console.log('1️⃣ Testing health endpoint...');
        const healthResponse = await axios.get(`${BASE_URL}/api/health`);
        console.log('✅ Health check:', healthResponse.data.status);

        // Test 2: API Info
        console.log('\n2️⃣ Testing info endpoint...');
        const infoResponse = await axios.get(`${BASE_URL}/api/info`);
        console.log('✅ API Info:', infoResponse.data.name);
        console.log('   Features:', infoResponse.data.features.join(', '));

        // Test 3: Background Styles
        console.log('\n3️⃣ Testing background styles...');
        const stylesResponse = await axios.get(`${BASE_URL}/api/process/styles`);
        console.log('✅ Background styles loaded:', Object.keys(stylesResponse.data.styles).length);
        console.log('   Available styles:', Object.keys(stylesResponse.data.styles).join(', '));

        // Test 4: API Key Validation
        console.log('\n4️⃣ Testing API key validation...');
        try {
            const testResponse = await axios.post(`${BASE_URL}/api/process/test`, {
                apiKey: TEST_API_KEY
            });
            console.log('✅ API key validation:', testResponse.data.message);
        } catch (error) {
            console.log('⚠️ API key validation failed (expected if no real key):', error.response?.data?.message);
        }

        // Test 5: Image Processing (Mock)
        console.log('\n5️⃣ Testing image processing endpoint...');
        try {
            const processResponse = await axios.post(`${BASE_URL}/api/process/single`, {
                apiKey: TEST_API_KEY,
                imageUrl: TEST_IMAGE_URL,
                backgroundStyle: 'minimalist',
                upscaleFactor: 2,
                generateCount: 2,
                finalPolish: false,
                useInpainting: true
            });
            console.log('✅ Processing started:', processResponse.data.jobId);
            
            // Test status endpoint
            const jobId = processResponse.data.jobId;
            setTimeout(async () => {
                try {
                    const statusResponse = await axios.get(`${BASE_URL}/api/status/${jobId}`);
                    console.log('✅ Status check:', statusResponse.data.status);
                } catch (error) {
                    console.log('⚠️ Status check failed:', error.response?.data?.message);
                }
            }, 1000);
            
        } catch (error) {
            console.log('⚠️ Processing failed (expected without real API key):', error.response?.data?.message);
        }

        // Test 6: Frontend Assets
        console.log('\n6️⃣ Testing frontend assets...');
        const frontendResponse = await axios.get(`${BASE_URL}/`);
        console.log('✅ Frontend loaded, size:', frontendResponse.data.length, 'bytes');

        const cssResponse = await axios.get(`${BASE_URL}/styles.css`);
        console.log('✅ CSS loaded, size:', cssResponse.data.length, 'bytes');

        const jsResponse = await axios.get(`${BASE_URL}/script.js`);
        console.log('✅ JavaScript loaded, size:', jsResponse.data.length, 'bytes');

        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📋 Test Summary:');
        console.log('   ✅ Health check: OK');
        console.log('   ✅ API info: OK');
        console.log('   ✅ Background styles: OK');
        console.log('   ✅ Frontend assets: OK');
        console.log('   ⚠️ API key validation: Needs real key');
        console.log('   ⚠️ Image processing: Needs real key');

        console.log('\n🔧 Next steps:');
        console.log('   1. Add your Runware API key to .env file');
        console.log('   2. Test with real image processing');
        console.log('   3. Try different background styles');
        console.log('   4. Test batch processing');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('   Status:', error.response.status);
            console.error('   Data:', error.response.data);
        }
        process.exit(1);
    }
}

// Test configuration validation
function testConfiguration() {
    console.log('🔧 Testing configuration...\n');

    // Check if .env exists
    const envExists = fs.existsSync('.env');
    console.log(envExists ? '✅ .env file exists' : '⚠️ .env file missing (copy from .env.example)');

    // Check required directories
    const requiredDirs = [
        'backend/uploads/input',
        'backend/uploads/output',
        'logs'
    ];

    requiredDirs.forEach(dir => {
        const exists = fs.existsSync(dir);
        console.log(exists ? `✅ Directory ${dir} exists` : `⚠️ Directory ${dir} missing`);
    });

    // Check package.json
    const packageExists = fs.existsSync('package.json');
    console.log(packageExists ? '✅ package.json exists' : '❌ package.json missing');

    if (packageExists) {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        console.log(`✅ Project: ${packageJson.name} v${packageJson.version}`);
    }

    console.log('');
}

// Performance test
async function performanceTest() {
    console.log('⚡ Running performance tests...\n');

    const endpoints = [
        '/api/health',
        '/api/info',
        '/api/process/styles',
        '/',
        '/styles.css',
        '/script.js'
    ];

    for (const endpoint of endpoints) {
        const start = Date.now();
        try {
            await axios.get(`${BASE_URL}${endpoint}`);
            const duration = Date.now() - start;
            console.log(`✅ ${endpoint}: ${duration}ms`);
        } catch (error) {
            console.log(`❌ ${endpoint}: Failed`);
        }
    }

    console.log('');
}

// Main execution
async function main() {
    console.log('🎨 Runware Product Studio - API Test Suite\n');
    console.log('==========================================\n');

    // Test configuration first
    testConfiguration();

    // Test performance
    await performanceTest();

    // Test API functionality
    await testAPI();

    console.log('\n==========================================');
    console.log('🏁 Test suite completed!');
    console.log('');
    console.log('💡 Tips:');
    console.log('   - Make sure server is running on port 3000');
    console.log('   - Add RUNWARE_API_KEY to .env for full testing');
    console.log('   - Check logs/ directory for detailed logs');
    console.log('   - Visit http://localhost:3000 to use the UI');
}

// Handle errors
process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled error:', error.message);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testAPI, testConfiguration, performanceTest };
