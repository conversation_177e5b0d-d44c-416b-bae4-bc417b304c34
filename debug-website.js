#!/usr/bin/env node

/**
 * Debug script to test website functionality step by step
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function debugWebsite() {
    console.log('🔍 Debugging Runware Product Studio Website...\n');

    try {
        // Test 1: Check if server is running
        console.log('1️⃣ Checking server status...');
        const healthResponse = await axios.get(`${BASE_URL}/api/health`);
        console.log('✅ Server is running:', healthResponse.data.status);

        // Test 2: Check frontend assets
        console.log('\n2️⃣ Checking frontend assets...');
        
        const htmlResponse = await axios.get(`${BASE_URL}/`);
        console.log('✅ HTML loaded, size:', htmlResponse.data.length, 'bytes');
        
        // Check if HTML contains expected elements
        const html = htmlResponse.data;
        const checks = [
            { name: 'Help button', pattern: 'onclick="showHelp()"' },
            { name: 'Settings button', pattern: 'onclick="showSettings()"' },
            { name: 'Help modal', pattern: 'id="helpModal"' },
            { name: 'Settings modal', pattern: 'id="settingsModal"' },
            { name: 'Script tag', pattern: 'script.js' },
            { name: 'Font Awesome', pattern: 'font-awesome' },
            { name: 'Upload area', pattern: 'id="uploadArea"' },
            { name: 'API key input', pattern: 'id="apiKey"' }
        ];

        checks.forEach(check => {
            const found = html.includes(check.pattern);
            console.log(found ? '✅' : '❌', check.name, found ? 'found' : 'missing');
        });

        // Test 3: Check JavaScript file
        console.log('\n3️⃣ Checking JavaScript file...');
        const jsResponse = await axios.get(`${BASE_URL}/script.js`);
        console.log('✅ JavaScript loaded, size:', jsResponse.data.length, 'bytes');
        
        // Check if JS contains expected functions
        const js = jsResponse.data;
        const jsFunctions = [
            'showHelp',
            'showSettings',
            'openModal',
            'closeModal',
            'toggleApiKeyVisibility',
            'loadFromUrl',
            'loadExampleUrl',
            'goToStep',
            'startProcessing',
            'cancelProcessing'
        ];

        jsFunctions.forEach(func => {
            const found = js.includes(`function ${func}`) || js.includes(`${func} =`) || js.includes(`${func}(`);
            console.log(found ? '✅' : '❌', `Function ${func}`, found ? 'found' : 'missing');
        });

        // Test 4: Check CSS file
        console.log('\n4️⃣ Checking CSS file...');
        const cssResponse = await axios.get(`${BASE_URL}/styles.css`);
        console.log('✅ CSS loaded, size:', cssResponse.data.length, 'bytes');
        
        // Check if CSS contains expected classes
        const css = cssResponse.data;
        const cssClasses = [
            '.modal',
            '.modal.active',
            '.btn',
            '.btn-primary',
            '.upload-area',
            '.preview-area'
        ];

        cssClasses.forEach(cls => {
            const found = css.includes(cls);
            console.log(found ? '✅' : '❌', `CSS class ${cls}`, found ? 'found' : 'missing');
        });

        // Test 5: Check API endpoints
        console.log('\n5️⃣ Checking API endpoints...');
        
        const endpoints = [
            '/api/health',
            '/api/info',
            '/api/process/styles'
        ];

        for (const endpoint of endpoints) {
            try {
                const response = await axios.get(`${BASE_URL}${endpoint}`);
                console.log('✅', endpoint, 'working, status:', response.status);
            } catch (error) {
                console.log('❌', endpoint, 'failed:', error.response?.status || error.message);
            }
        }

        // Test 6: Test specific functionality
        console.log('\n6️⃣ Testing specific functionality...');
        
        // Test background styles
        try {
            const stylesResponse = await axios.get(`${BASE_URL}/api/process/styles`);
            const styles = stylesResponse.data.styles;
            console.log('✅ Background styles loaded:', Object.keys(styles).length, 'styles');
            console.log('   Available:', Object.keys(styles).join(', '));
        } catch (error) {
            console.log('❌ Background styles failed:', error.message);
        }

        // Test 7: Check for common issues
        console.log('\n7️⃣ Checking for common issues...');
        
        // Check if Font Awesome is accessible
        try {
            await axios.get('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
            console.log('✅ Font Awesome CDN accessible');
        } catch (error) {
            console.log('❌ Font Awesome CDN not accessible:', error.message);
        }

        // Check if Google Fonts is accessible
        try {
            await axios.get('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            console.log('✅ Google Fonts accessible');
        } catch (error) {
            console.log('❌ Google Fonts not accessible:', error.message);
        }

        console.log('\n🎯 Debug Summary:');
        console.log('   - Server is running and responding');
        console.log('   - All main files are loading');
        console.log('   - JavaScript functions are defined');
        console.log('   - CSS classes are available');
        console.log('   - API endpoints are working');
        
        console.log('\n💡 Recommendations:');
        console.log('   1. Open browser developer tools (F12)');
        console.log('   2. Check Console tab for JavaScript errors');
        console.log('   3. Check Network tab for failed resource loads');
        console.log('   4. Try clicking buttons manually');
        console.log('   5. Check if Font Awesome icons are loading');

        console.log('\n🧪 Manual Test Steps:');
        console.log('   1. Open http://localhost:3000 in browser');
        console.log('   2. Click "Help" button in header');
        console.log('   3. Click "Settings" button in header');
        console.log('   4. Switch between File/URL tabs');
        console.log('   5. Enter a URL and click "Load"');
        console.log('   6. Try example URL buttons');

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n🚨 Server is not running!');
            console.log('   Run: npm start');
        }
    }
}

// Run debug
if (require.main === module) {
    debugWebsite().catch(console.error);
}

module.exports = { debugWebsite };
