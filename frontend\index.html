<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Runware Product Studio</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-camera-retro"></i>
                    <h1>Runware Product Studio</h1>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="showHelp()">
                        <i class="fas fa-question-circle"></i> Help
                    </button>
                    <button class="btn btn-primary" onclick="showSettings()">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Step Indicator -->
            <div class="steps">
                <div class="step active" data-step="1">
                    <div class="step-icon"><i class="fas fa-upload"></i></div>
                    <div class="step-label">Upload</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-icon"><i class="fas fa-sliders-h"></i></div>
                    <div class="step-label">Settings</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-icon"><i class="fas fa-magic"></i></div>
                    <div class="step-label">Process</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-icon"><i class="fas fa-download"></i></div>
                    <div class="step-label">Results</div>
                </div>
            </div>

            <!-- Content Sections -->

            <!-- Step 1: Upload -->
            <section id="upload-section" class="section active">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-upload"></i> Upload Product Image</h2>
                        <p>Upload your product image or provide a URL to get started</p>
                    </div>
                    <div class="card-body">
                        <!-- Upload Methods -->
                        <div class="upload-methods">
                            <div class="method-tabs">
                                <button class="method-tab active" data-method="file">
                                    <i class="fas fa-file-upload"></i> Upload File
                                </button>
                                <button class="method-tab" data-method="url">
                                    <i class="fas fa-link"></i> Image URL
                                </button>
                            </div>

                            <!-- File Upload -->
                            <div id="upload-file" class="upload-method active">
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <h3>Drag & Drop Your Image Here</h3>
                                        <p>or click to browse files</p>
                                        <div class="upload-info">
                                            <span>Supported: JPG, PNG, WEBP</span>
                                            <span>Max size: 10MB</span>
                                        </div>
                                    </div>
                                    <input type="file" id="fileInput" accept=".jpg,.jpeg,.png,.webp" style="display: none;">
                                </div>
                            </div>

                            <!-- URL Input -->
                            <div id="upload-url" class="upload-method">
                                <div class="url-input-group">
                                    <input type="url" id="imageUrl" placeholder="https://example.com/product-image.jpg">
                                    <button class="btn btn-primary" onclick="loadFromUrl()">
                                        <i class="fas fa-download"></i> Load
                                    </button>
                                </div>
                                <div class="url-examples">
                                    <p>Example URLs:</p>
                                    <button class="example-url" onclick="loadExampleUrl('https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500')">
                                        Nike Shoes
                                    </button>
                                    <button class="example-url" onclick="loadExampleUrl('https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500')">
                                        Watch
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Area -->
                        <div id="preview-area" class="preview-area" style="display: none;">
                            <div class="preview-header">
                                <h3>Image Preview</h3>
                                <button class="btn btn-secondary btn-sm" onclick="clearPreview()">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                            </div>
                            <div class="preview-content">
                                <img id="previewImage" alt="Preview">
                                <div class="preview-info">
                                    <div class="info-item">
                                        <label>File:</label>
                                        <span id="fileName">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Size:</label>
                                        <span id="fileSize">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Dimensions:</label>
                                        <span id="imageDimensions">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Next Button -->
                        <div class="section-footer">
                            <button class="btn btn-primary btn-lg" id="nextToSettings" onclick="goToStep(2)" disabled>
                                <i class="fas fa-arrow-right"></i> Continue to Settings
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: Settings -->
            <section id="settings-section" class="section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-sliders-h"></i> Processing Settings</h2>
                        <p>Configure how your product image will be processed</p>
                    </div>
                    <div class="card-body">
                        <form id="settingsForm" class="settings-form">
                            <!-- API Key -->
                            <div class="form-group">
                                <label for="apiKey">
                                    <i class="fas fa-key"></i> Runware API Key
                                    <span class="required">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" id="apiKey" placeholder="Enter your Runware API key" required>
                                    <button type="button" class="btn btn-secondary" onclick="toggleApiKeyVisibility()">
                                        <i class="fas fa-eye" id="apiKeyToggle"></i>
                                    </button>
                                </div>
                                <small>Get your API key from <a href="https://runware.ai" target="_blank">runware.ai</a></small>
                            </div>

                            <!-- Background Style -->
                            <div class="form-group">
                                <label>
                                    <i class="fas fa-palette"></i> Background Style
                                </label>
                                <div class="style-grid" id="styleGrid">
                                    <!-- Styles will be loaded dynamically -->
                                </div>
                            </div>

                            <!-- Advanced Options -->
                            <div class="form-group">
                                <label>
                                    <i class="fas fa-cogs"></i> Advanced Options
                                </label>
                                <div class="advanced-options">
                                    <div class="option-row">
                                        <label for="upscaleFactor">Image Enhancement</label>
                                        <select id="upscaleFactor">
                                            <option value="1">No upscaling</option>
                                            <option value="2" selected>2x Quality (Recommended)</option>
                                            <option value="3">3x Quality</option>
                                            <option value="4">4x Quality (Slow)</option>
                                        </select>
                                    </div>
                                    <div class="option-row">
                                        <label for="generateCount">Background Variants</label>
                                        <select id="generateCount">
                                            <option value="2">2 variants</option>
                                            <option value="4" selected>4 variants (Recommended)</option>
                                            <option value="6">6 variants</option>
                                            <option value="8">8 variants</option>
                                        </select>
                                    </div>
                                    <div class="option-row">
                                        <label for="backgroundMethod">Background Method</label>
                                        <select id="backgroundMethod">
                                            <option value="inpainting" selected>Inpainting (Better Integration)</option>
                                            <option value="composition">Composition (More Creative)</option>
                                        </select>
                                        <small>Choose how backgrounds are generated</small>
                                    </div>
                                    <div class="option-row">
                                        <label for="finalPolish">Final Polish</label>
                                        <label class="toggle">
                                            <input type="checkbox" id="finalPolish">
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <small>Extra enhancement for top 2 images</small>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="section-footer">
                            <button class="btn btn-secondary" onclick="goToStep(1)">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button class="btn btn-primary btn-lg" onclick="startProcessing()">
                                <i class="fas fa-magic"></i> Start Processing
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 3: Processing -->
            <section id="processing-section" class="section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-magic"></i> Processing Your Image</h2>
                        <p>AI is working its magic on your product image</p>
                    </div>
                    <div class="card-body">
                        <!-- Progress Bar -->
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="progress-text">
                                <span id="progressPercentage">0%</span>
                                <span id="progressStatus">Initializing...</span>
                            </div>
                        </div>

                        <!-- Processing Steps -->
                        <div class="processing-steps">
                            <div class="processing-step" id="step-bg-removal">
                                <div class="step-icon"><i class="fas fa-scissors"></i></div>
                                <div class="step-content">
                                    <h4>Background Removal</h4>
                                    <p>Intelligently separating product from background</p>
                                </div>
                                <div class="step-status">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="processing-step" id="step-enhancement">
                                <div class="step-icon"><i class="fas fa-search-plus"></i></div>
                                <div class="step-content">
                                    <h4>Image Enhancement</h4>
                                    <p>Improving quality and resolution</p>
                                </div>
                                <div class="step-status">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="processing-step" id="step-background">
                                <div class="step-icon"><i class="fas fa-image"></i></div>
                                <div class="step-content">
                                    <h4>Background Generation</h4>
                                    <p>Creating professional backgrounds</p>
                                </div>
                                <div class="step-status">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Cost Tracker -->
                        <div class="cost-tracker">
                            <div class="cost-item">
                                <label>Estimated Cost:</label>
                                <span id="estimatedCost">$0.02 - $0.05</span>
                            </div>
                            <div class="cost-item">
                                <label>Elapsed Time:</label>
                                <span id="elapsedTime">0s</span>
                            </div>
                        </div>

                        <div class="section-footer">
                            <button class="btn btn-secondary" onclick="cancelProcessing()" id="cancelBtn">
                                <i class="fas fa-stop"></i> Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 4: Results -->
            <section id="results-section" class="section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-check-circle"></i> Results</h2>
                        <p>Your professional product images are ready!</p>
                    </div>
                    <div class="card-body">
                        <!-- Results Summary -->
                        <div class="results-summary" id="resultsSummary" style="display: none;">
                            <div class="summary-item">
                                <label>Processing Time:</label>
                                <span id="totalProcessingTime">-</span>
                            </div>
                            <div class="summary-item">
                                <label>Total Cost:</label>
                                <span id="totalCost">-</span>
                            </div>
                            <div class="summary-item">
                                <label>Images Generated:</label>
                                <span id="imageCount">-</span>
                            </div>
                        </div>

                        <!-- Results Gallery -->
                        <div class="results-gallery" id="resultsGallery">
                            <!-- Results will be populated here -->
                        </div>

                        <!-- Error Display -->
                        <div class="error-display" id="errorDisplay" style="display: none;">
                            <div class="error-content">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h3>Processing Failed</h3>
                                <p id="errorMessage">An error occurred during processing.</p>
                                <button class="btn btn-primary" onclick="goToStep(2)">
                                    <i class="fas fa-redo"></i> Try Again
                                </button>
                            </div>
                        </div>

                        <div class="section-footer" id="resultsFooter" style="display: none;">
                            <button class="btn btn-secondary" onclick="startNewProject()">
                                <i class="fas fa-plus"></i> New Project
                            </button>
                            <button class="btn btn-primary" onclick="downloadAll()">
                                <i class="fas fa-download"></i> Download All
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>App Settings</h3>
                <button class="modal-close" onclick="closeModal('settingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>API Key Storage</label>
                    <label class="toggle">
                        <input type="checkbox" id="saveApiKey" checked>
                        <span class="toggle-slider"></span>
                    </label>
                    <small>Save API key locally (browser storage)</small>
                </div>
                <div class="setting-group">
                    <label>Auto-cleanup uploads</label>
                    <label class="toggle">
                        <input type="checkbox" id="autoCleanup" checked>
                        <span class="toggle-slider"></span>
                    </label>
                    <small>Automatically delete uploaded files after processing</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="saveSettings()">Save</button>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>How to Use</h3>
                <button class="modal-close" onclick="closeModal('helpModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="help-content">
                    <div class="help-step">
                        <h4>1. Upload Your Product Image</h4>
                        <p>Upload a clear product image or provide a URL. Best results with products on plain backgrounds.</p>
                    </div>
                    <div class="help-step">
                        <h4>2. Configure Settings</h4>
                        <p>Enter your Runware API key and choose background style. Adjust quality and variant settings as needed.</p>
                    </div>
                    <div class="help-step">
                        <h4>3. AI Processing</h4>
                        <p>Our AI will remove the background, enhance quality, and generate professional backgrounds.</p>
                    </div>
                    <div class="help-step">
                        <h4>4. Download Results</h4>
                        <p>Download individual images or all at once. Use them for your e-commerce listings!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>