// Main application state
const AppState = {
    currentStep: 1,
    uploadedFile: null,
    imageUrl: null,
    jobId: null,
    processingInterval: null,
    settings: {
        apiKey: '',
        backgroundStyle: 'minimalist',
        upscaleFactor: 2,
        generateCount: 4,
        finalPolish: false,
        backgroundMethod: 'inpainting'
    }
};

// DOM Elements
const elements = {
    // Upload elements
    uploadArea: document.getElementById('uploadArea'),
    fileInput: document.getElementById('fileInput'),
    imageUrl: document.getElementById('imageUrl'),
    previewArea: document.getElementById('preview-area'),
    previewImage: document.getElementById('previewImage'),
    fileName: document.getElementById('fileName'),
    fileSize: document.getElementById('fileSize'),
    imageDimensions: document.getElementById('imageDimensions'),
    nextToSettings: document.getElementById('nextToSettings'),

    // Settings elements
    apiKey: document.getElementById('apiKey'),
    styleGrid: document.getElementById('styleGrid'),
    upscaleFactor: document.getElementById('upscaleFactor'),
    generateCount: document.getElementById('generateCount'),
    finalPolish: document.getElementById('finalPolish'),
    backgroundMethod: document.getElementById('backgroundMethod'),

    // Processing elements
    progressFill: document.getElementById('progressFill'),
    progressPercentage: document.getElementById('progressPercentage'),
    progressStatus: document.getElementById('progressStatus'),
    elapsedTime: document.getElementById('elapsedTime'),
    cancelBtn: document.getElementById('cancelBtn'),

    // Results elements
    resultsSummary: document.getElementById('resultsSummary'),
    resultsGallery: document.getElementById('resultsGallery'),
    errorDisplay: document.getElementById('errorDisplay'),
    errorMessage: document.getElementById('errorMessage'),
    resultsFooter: document.getElementById('resultsFooter'),
    totalProcessingTime: document.getElementById('totalProcessingTime'),
    totalCost: document.getElementById('totalCost'),
    imageCount: document.getElementById('imageCount')
};

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadSettings();
    setupEventListeners();
    loadBackgroundStyles();
});

// Modal Functions
function showSettings() {
    openModal('settingsModal');
}

function showHelp() {
    openModal('helpModal');
}

function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// API Key Visibility Toggle
function toggleApiKeyVisibility() {
    const apiKeyInput = elements.apiKey;
    const toggleIcon = document.getElementById('apiKeyToggle');

    if (apiKeyInput && toggleIcon) {
        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            apiKeyInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }
}

function initializeApp() {
    console.log('🚀 Runware Product Studio initialized');

    // Show welcome message if first time
    if (!localStorage.getItem('hasVisited')) {
        showToast('Welcome to Runware Product Studio! Upload an image to get started.', 'info');
        localStorage.setItem('hasVisited', 'true');
    }
}

function loadSettings() {
    // Load saved API key
    const savedApiKey = localStorage.getItem('runware_api_key');
    if (savedApiKey && elements.apiKey) {
        elements.apiKey.value = savedApiKey;
        AppState.settings.apiKey = savedApiKey;
    }

    // Load other settings
    const savedSettings = localStorage.getItem('app_settings');
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            Object.assign(AppState.settings, settings);
            updateSettingsUI();
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
}

function saveSettings() {
    // Save API key if enabled
    const saveApiKey = document.getElementById('saveApiKey').checked;
    if (saveApiKey && AppState.settings.apiKey) {
        localStorage.setItem('runware_api_key', AppState.settings.apiKey);
    } else {
        localStorage.removeItem('runware_api_key');
    }

    // Save other settings
    localStorage.setItem('app_settings', JSON.stringify(AppState.settings));

    showToast('Settings saved successfully', 'success');
    closeModal('settingsModal');
}

function updateSettingsUI() {
    if (elements.apiKey) elements.apiKey.value = AppState.settings.apiKey;
    if (elements.upscaleFactor) elements.upscaleFactor.value = AppState.settings.upscaleFactor;
    if (elements.generateCount) elements.generateCount.value = AppState.settings.generateCount;
    if (elements.finalPolish) elements.finalPolish.checked = AppState.settings.finalPolish;
    if (elements.backgroundMethod) elements.backgroundMethod.value = AppState.settings.backgroundMethod;

    // Update selected background style
    updateSelectedStyle(AppState.settings.backgroundStyle);
}

function setupEventListeners() {
    // Upload method tabs
    document.querySelectorAll('.method-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const method = this.dataset.method;
            switchUploadMethod(method);
        });
    });

    // File upload
    if (elements.uploadArea) {
        elements.uploadArea.addEventListener('click', () => elements.fileInput.click());
        elements.uploadArea.addEventListener('dragover', handleDragOver);
        elements.uploadArea.addEventListener('dragleave', handleDragLeave);
        elements.uploadArea.addEventListener('drop', handleDrop);
    }

    if (elements.fileInput) {
        elements.fileInput.addEventListener('change', handleFileSelect);
    }

    // Settings form
    if (elements.apiKey) {
        elements.apiKey.addEventListener('input', function() {
            AppState.settings.apiKey = this.value;
        });
    }

    ['upscaleFactor', 'generateCount', 'finalPolish', 'backgroundMethod'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', function() {
                const value = this.type === 'checkbox' ? this.checked : this.value;
                AppState.settings[id] = this.type === 'number' || id.includes('Factor') || id.includes('Count')
                    ? parseInt(value) : value;
            });
        }
    });

    // Modal close handlers
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });

    // Escape key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.modal.active').forEach(modal => {
                closeModal(modal.id);
            });
        }
    });
}

// Upload Methods
function switchUploadMethod(method) {
    // Update tabs
    document.querySelectorAll('.method-tab').forEach(tab => {
        tab.classList.toggle('active', tab.dataset.method === method);
    });

    // Update methods
    document.querySelectorAll('.upload-method').forEach(methodDiv => {
        methodDiv.classList.toggle('active', methodDiv.id === `upload-${method}`);
    });
}

// File Upload Handlers
function handleDragOver(e) {
    e.preventDefault();
    elements.uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    elements.uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    elements.uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

async function handleFile(file) {
    // Validate file
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
        showToast('Invalid file type. Please upload JPG, PNG, or WEBP images.', 'error');
        return;
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        showToast('File too large. Maximum size is 10MB.', 'error');
        return;
    }

    showLoading(true);

    try {
        // Upload file
        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Upload failed');
        }

        // Store file info
        AppState.uploadedFile = result.file;
        AppState.imageUrl = null;

        // Show preview
        showPreview(result.file.url, result.file);
        showToast('File uploaded successfully!', 'success');

    } catch (error) {
        console.error('Upload error:', error);
        showToast(`Upload failed: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

async function loadFromUrl() {
    const url = elements.imageUrl.value.trim();

    if (!url) {
        showToast('Please enter an image URL', 'error');
        return;
    }

    // Basic URL validation
    try {
        new URL(url);
    } catch {
        showToast('Invalid URL format', 'error');
        return;
    }

    showLoading(true);

    try {
        // Test if image loads
        const img = new Image();
        img.crossOrigin = 'anonymous';

        await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = url;
        });

        // Store URL info
        AppState.imageUrl = url;
        AppState.uploadedFile = null;

        // Show preview
        showPreview(url, {
            originalName: url.split('/').pop() || 'Image from URL',
            size: 'Unknown',
            url: url
        });

        showToast('Image loaded successfully!', 'success');

    } catch (error) {
        console.error('URL load error:', error);
        showToast('Failed to load image from URL. Please check the URL.', 'error');
    } finally {
        showLoading(false);
    }
}

function loadExampleUrl(url) {
    elements.imageUrl.value = url;
    loadFromUrl();
}

function showPreview(imageSrc, fileInfo) {
    elements.previewImage.src = imageSrc;
    elements.fileName.textContent = fileInfo.originalName || 'Unknown';
    elements.fileSize.textContent = fileInfo.size ? formatFileSize(fileInfo.size) : 'Unknown';

    // Get image dimensions
    elements.previewImage.onload = function() {
        elements.imageDimensions.textContent = `${this.naturalWidth} × ${this.naturalHeight}px`;
    };

    elements.previewArea.style.display = 'block';
    elements.nextToSettings.disabled = false;
}

function clearPreview() {
    elements.previewArea.style.display = 'none';
    elements.nextToSettings.disabled = true;
    AppState.uploadedFile = null;
    AppState.imageUrl = null;
    elements.fileInput.value = '';
    elements.imageUrl.value = '';
}

// Background Styles
async function loadBackgroundStyles() {
    try {
        const response = await fetch('/api/process/styles');
        const data = await response.json();

        if (data.success) {
            renderStyleGrid(data.styles);
        }
    } catch (error) {
        console.error('Failed to load styles:', error);
        renderDefaultStyles();
    }
}

function renderStyleGrid(styles) {
    const grid = elements.styleGrid;
    grid.innerHTML = '';

    Object.entries(styles).forEach(([key, style]) => {
        const div = document.createElement('div');
        div.className = 'style-option';
        div.dataset.style = key;

        if (key === AppState.settings.backgroundStyle) {
            div.classList.add('selected');
        }

        div.innerHTML = `
            <div class="emoji">${style.preview || '🎨'}</div>
            <div class="name">${style.name}</div>
            <div class="description">${style.description}</div>
        `;

        div.addEventListener('click', () => selectStyle(key));
        grid.appendChild(div);
    });
}

function renderDefaultStyles() {
    const defaultStyles = {
        minimalist: { name: "Minimalist", description: "Clean white background", preview: "🤍" },
        luxury: { name: "Luxury", description: "Premium marble surface", preview: "✨" },
        natural: { name: "Natural", description: "Wooden surface", preview: "🌿" },
        modern: { name: "Modern", description: "Contemporary concrete", preview: "🏢" }
    };

    renderStyleGrid(defaultStyles);
}

function selectStyle(styleKey) {
    AppState.settings.backgroundStyle = styleKey;
    updateSelectedStyle(styleKey);
}

function updateSelectedStyle(styleKey) {
    document.querySelectorAll('.style-option').forEach(option => {
        option.classList.toggle('selected', option.dataset.style === styleKey);
    });
}

// Navigation
function goToStep(step) {
    if (step < 1 || step > 4) return;

    // Validate before moving to next step
    if (step === 2 && !AppState.uploadedFile && !AppState.imageUrl) {
        showToast('Please upload an image first', 'error');
        return;
    }

    if (step === 3 && !AppState.settings.apiKey) {
        showToast('Please enter your Runware API key', 'error');
        return;
    }

    // Update step indicator
    document.querySelectorAll('.step').forEach((stepEl, index) => {
        stepEl.classList.remove('active', 'completed');
        if (index + 1 < step) {
            stepEl.classList.add('completed');
        } else if (index + 1 === step) {
            stepEl.classList.add('active');
        }
    });

    // Show corresponding section
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });

    const targetSection = document.getElementById(`${getSectionName(step)}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    AppState.currentStep = step;
}

function getSectionName(step) {
    const names = { 1: 'upload', 2: 'settings', 3: 'processing', 4: 'results' };
    return names[step];
}

// Processing
async function startProcessing() {
    // Validate inputs
    if (!AppState.settings.apiKey) {
        showToast('Please enter your Runware API key', 'error');
        return;
    }

    if (!AppState.uploadedFile && !AppState.imageUrl) {
        showToast('Please upload an image first', 'error');
        return;
    }

    // Go to processing step
    goToStep(3);

    // Prepare request data
    const requestData = {
        apiKey: AppState.settings.apiKey,
        backgroundStyle: AppState.settings.backgroundStyle,
        upscaleFactor: AppState.settings.upscaleFactor,
        generateCount: AppState.settings.generateCount,
        finalPolish: AppState.settings.finalPolish,
        useInpainting: AppState.settings.backgroundMethod === 'inpainting'
    };

    if (AppState.uploadedFile) {
        requestData.filename = AppState.uploadedFile.filename;
    } else {
        requestData.imageUrl = AppState.imageUrl;
    }

    try {
        // Start processing
        const response = await fetch('/api/process/single', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Processing failed to start');
        }

        AppState.jobId = result.jobId;
        showToast('Processing started successfully!', 'success');

        // Start polling for status
        startStatusPolling();

    } catch (error) {
        console.error('Processing error:', error);
        showToast(`Failed to start processing: ${error.message}`, 'error');
        showProcessingError(error.message);
    }
}

function startStatusPolling() {
    if (AppState.processingInterval) {
        clearInterval(AppState.processingInterval);
    }

    const startTime = Date.now();

    AppState.processingInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/status/${AppState.jobId}`);
            const status = await response.json();

            if (!response.ok) {
                throw new Error(status.message || 'Failed to get status');
            }

            updateProcessingUI(status);

            // Update elapsed time
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            elements.elapsedTime.textContent = `${elapsed}s`;

            // Check if completed
            if (status.status === 'completed') {
                clearInterval(AppState.processingInterval);
                AppState.processingInterval = null;
                showResults(status); // Pass the entire status object
            } else if (status.status === 'failed') {
                clearInterval(AppState.processingInterval);
                AppState.processingInterval = null;
                showProcessingError(status.error || 'Processing failed');
            }

        } catch (error) {
            console.error('Status polling error:', error);
            clearInterval(AppState.processingInterval);
            AppState.processingInterval = null;
            showProcessingError(error.message);
        }
    }, 2000); // Poll every 2 seconds
}

function updateProcessingUI(status) {
    const progress = status.progress?.percentage || 0;
    const statusText = status.currentStep || 'Processing...';

    // Update progress bar
    if (elements.progressFill) {
        elements.progressFill.style.width = `${progress}%`;
    }
    if (elements.progressPercentage) {
        elements.progressPercentage.textContent = `${progress}%`;
    }
    if (elements.progressStatus) {
        elements.progressStatus.textContent = statusText;
    }

    // Update step indicators
    const steps = ['step-bg-removal', 'step-enhancement', 'step-background'];
    steps.forEach((stepId, index) => {
        const stepEl = document.getElementById(stepId);
        if (!stepEl) return;

        const stepStatus = stepEl.querySelector('.step-status i');
        if (!stepStatus) return;

        if (progress > (index + 1) * 33) {
            stepStatus.className = 'fas fa-check-circle';
            stepStatus.style.color = '#10b981'; // success color
        } else if (progress > index * 33) {
            stepStatus.className = 'fas fa-spinner fa-spin';
            stepStatus.style.color = '#6366f1'; // primary color
        } else {
            stepStatus.className = 'fas fa-clock';
            stepStatus.style.color = '#9ca3af'; // gray color
        }
    });
}

function showResults(results) {
    goToStep(4);

    // Show results summary
    elements.resultsSummary.style.display = 'block';
    elements.totalProcessingTime.textContent = results.processingTime || 'Unknown';
    elements.totalCost.textContent = `$${results.totalCost || '0.00'}`;
    elements.imageCount.textContent = results.images?.length || 0;

    // Populate gallery
    populateResultsGallery(results.images || []);

    // Show footer
    elements.resultsFooter.style.display = 'block';

    showToast('Processing completed successfully!', 'success');
}

function populateResultsGallery(images) {
    const gallery = elements.resultsGallery;
    gallery.innerHTML = '';

    images.forEach((image, index) => {
        const div = document.createElement('div');
        div.className = 'result-item';

        div.innerHTML = `
            <img src="${image.url}" alt="${image.title || `Result ${index + 1}`}" loading="lazy">
            <div class="result-item-content">
                <h4 class="result-item-title">${image.title || `Variant ${index + 1}`}</h4>
                <p class="result-item-description">${image.description || 'Professional product image'}</p>
                <div class="result-item-actions">
                    <button class="btn btn-secondary btn-sm" onclick="viewFullSize('${image.url}', '${image.title || `Variant ${index + 1}`}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="downloadImage('${image.url}', '${image.title || `variant_${index + 1}`}')">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        `;

        gallery.appendChild(div);
    });
}

function showProcessingError(errorMessage) {
    goToStep(4);

    // Hide results and show error
    elements.resultsSummary.style.display = 'none';
    elements.resultsFooter.style.display = 'none';
    elements.errorDisplay.style.display = 'block';
    elements.errorMessage.textContent = errorMessage;

    showToast(`Processing failed: ${errorMessage}`, 'error');
}

// Results handling
function showResults(result) {
    goToStep(4);

    // Handle different result formats
    let images = [];
    let processingTime = 'Unknown';
    let totalCost = '$0.00';

    if (result.result) {
        // From status API
        const res = result.result;
        processingTime = res.metadata?.processingTimeFormatted || 'Unknown';
        totalCost = res.metadata?.totalCostFormatted || '$0.00';

        if (res.results?.final) {
            images = res.results.final.map((url, index) => ({
                url: url,
                title: `Variant ${index + 1}`,
                description: `Professional product photo with ${res.metadata?.backgroundStyle || 'custom'} background`
            }));
        }
    } else if (result.images) {
        // Direct images array
        images = result.images;
        processingTime = result.processingTime || 'Unknown';
        totalCost = result.totalCost || '$0.00';
    }

    // Update summary
    elements.resultsSummary.style.display = 'block';
    elements.totalProcessingTime.textContent = processingTime;
    elements.totalCost.textContent = totalCost;
    elements.imageCount.textContent = images.length;

    // Show results gallery
    populateResultsGallery(images);

    // Show footer
    elements.resultsFooter.style.display = 'block';

    showToast('Processing completed successfully!', 'success');
}

// Download Functions

async function downloadImage(imageUrl, filename) {
    try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${filename.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
        link.click();

        URL.revokeObjectURL(link.href);
        showToast('Download started', 'success');

    } catch (error) {
        console.error('Download error:', error);
        showToast('Download failed', 'error');
    }
}

function downloadAll() {
    const images = document.querySelectorAll('.result-item img');
    const titles = document.querySelectorAll('.result-item-title');

    images.forEach((img, index) => {
        setTimeout(() => {
            const title = titles[index]?.textContent || `Image_${index + 1}`;
            downloadImage(img.src, title);
        }, index * 500); // Stagger downloads
    });
}

function viewFullSize(imageUrl, title) {
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 90vw; max-height: 90vh;">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" style="text-align: center; padding: 0;">
                <img src="${imageUrl}" alt="${title}" style="max-width: 100%; max-height: 70vh; object-fit: contain;">
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Processing Functions
function cancelProcessing() {
    if (AppState.processingInterval) {
        clearInterval(AppState.processingInterval);
        AppState.processingInterval = null;
    }

    if (AppState.jobId) {
        // Cancel the job on server
        fetch(`/api/cancel/${AppState.jobId}`, { method: 'POST' })
            .catch(error => console.error('Cancel error:', error));
    }

    showToast('Processing cancelled', 'warning');
    goToStep(2);
}

function startNewProject() {
    // Reset state
    AppState.currentStep = 1;
    AppState.uploadedFile = null;
    AppState.imageUrl = null;
    AppState.jobId = null;

    // Clear UI
    clearPreview();
    elements.resultsGallery.innerHTML = '';
    elements.resultsSummary.style.display = 'none';
    elements.resultsFooter.style.display = 'none';
    elements.errorDisplay.style.display = 'none';

    // Go to upload step
    goToStep(1);
    showToast('Ready for new project!', 'info');
}

// Toast Notifications
function showToast(message, type = 'info', duration = 5000) {
    const container = document.getElementById('toast-container');
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    toast.innerHTML = `
        <i class="${iconMap[type]}"></i>
        <span>${message}</span>
    `;

    container.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, duration);
}

// Loading Overlay
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// Utility Functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}