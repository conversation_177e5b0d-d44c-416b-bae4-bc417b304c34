// Main application state
const AppState = {
    currentStep: 1,
    uploadedFile: null,
    imageUrl: null,
    jobId: null,
    processingInterval: null,
    settings: {
        apiKey: '',
        backgroundStyle: 'minimalist',
        upscaleFactor: 2,
        generateCount: 4,
        finalPolish: false
    }
};

// DOM Elements
const elements = {
    // Upload elements
    uploadArea: document.getElementById('uploadArea'),
    fileInput: document.getElementById('fileInput'),
    imageUrl: document.getElementById('imageUrl'),
    previewArea: document.getElementById('preview-area'),
    previewImage: document.getElementById('previewImage'),
    fileName: document.getElementById('fileName'),
    fileSize: document.getElementById('fileSize'),
    imageDimensions: document.getElementById('imageDimensions'),
    nextToSettings: document.getElementById('nextToSettings'),
    
    // Settings elements
    apiKey: document.getElementById('apiKey'),
    styleGrid: document.getElementById('styleGrid'),
    upscaleFactor: document.getElementById('upscaleFactor'),
    generateCount: document.getElementById('generateCount'),
    finalPolish: document.getElementById('finalPolish'),
    
    // Processing elements
    progressFill: document.getElementById('progressFill'),
    progressPercentage: document.getElementById('progressPercentage'),
    progressStatus: document.getElementById('progressStatus'),
    elapsedTime: document.getElementById('elapsedTime'),
    cancelBtn: document.getElementById('cancelBtn'),
    
    // Results elements
    resultsSummary: document.getElementById('resultsSummary'),
    resultsGallery: document.getElementById('resultsGallery'),
    errorDisplay: document.getElementById('errorDisplay'),
    errorMessage: document.getElementById('errorMessage'),
    resultsFooter: document.getElementById('resultsFooter'),
    totalProcessingTime: document.getElementById('totalProcessingTime'),
    totalCost: document.getElementById('totalCost'),
    imageCount: document.getElementById('imageCount')
};

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadSettings();
    setupEventListeners();
    loadBackgroundStyles();
});

function initializeApp() {
    console.log('🚀 Runware Product Studio initialized');
    
    // Show welcome message if first time
    if (!localStorage.getItem('hasVisited')) {
        showToast('Welcome to Runware Product Studio! Upload an image to get started.', 'info');
        localStorage.setItem('hasVisited', 'true');
    }
}

function loadSettings() {
    // Load saved API key
    const savedApiKey = localStorage.getItem('runware_api_key');
    if (savedApiKey && elements.apiKey) {
        elements.apiKey.value = savedApiKey;
        AppState.settings.apiKey = savedApiKey;
    }
    
    // Load other settings
    const savedSettings = localStorage.getItem('app_settings');
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            Object.assign(AppState.settings, settings);
            updateSettingsUI();
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
}

function saveSettings() {
    // Save API key if enabled
    const saveApiKey = document.getElementById('saveApiKey').checked;
    if (saveApiKey && AppState.settings.apiKey) {
        localStorage.setItem('runware_api_key', AppState.settings.apiKey);
    } else {
        localStorage.removeItem('runware_api_key');
    }
    
    // Save other settings
    localStorage.setItem('app_settings', JSON.stringify(AppState.settings));
    
    showToast('Settings saved successfully', 'success');
    closeModal('settingsModal');
}

function updateSettingsUI() {
    if (elements.apiKey) elements.apiKey.value = AppState.settings.apiKey;
    if (elements.upscaleFactor) elements.upscaleFactor.value = AppState.settings.upscaleFactor;
    if (elements.generateCount) elements.generateCount.value = AppState.settings.generateCount;
    if (elements.finalPolish) elements.finalPolish.checked = AppState.settings.finalPolish;
    
    // Update selected background style
    updateSelectedStyle(AppState.settings.backgroundStyle);
}

function setupEventListeners() {
    // Upload method tabs
    document.querySelectorAll('.method-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const method = this.dataset.method;
            switchUploadMethod(method);
        });
    });
    
    // File upload
    if (elements.uploadArea) {
        elements.uploadArea.addEventListener('click', () => elements.fileInput.click());
        elements.uploadArea.addEventListener('dragover', handleDragOver);
        elements.uploadArea.addEventListener('dragleave', handleDragLeave);
        elements.uploadArea.addEventListener('drop', handleDrop);
    }
    
    if (elements.fileInput) {
        elements.fileInput.addEventListener('change', handleFileSelect);
    }
    
    // Settings form
    if (elements.apiKey) {
        elements.apiKey.addEventListener('input', function() {
            AppState.settings.apiKey = this.value;
        });
    }
    
    ['upscaleFactor', 'generateCount', 'finalPolish'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', function() {
                const value = this.type === 'checkbox' ? this.checked : this.value;
                AppState.settings[id] = this.type === 'number' || id.includes('Factor') || id.includes('Count') 
                    ? parseInt(value) : value;
            });
        }
    });
    
    // Modal close handlers
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });
    
    // Escape key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.modal.active').forEach(modal => {
                closeModal(modal.id);
            });
        }
    });
}

// Upload Methods
function switchUploadMethod(method) {
    // Update tabs
    document.querySelectorAll('.method-tab').forEach(tab => {
        tab.classList.toggle('active', tab.dataset.method === method);
    });
    
    // Update methods
    document.querySelectorAll('.upload-method').forEach(methodDiv => {
        methodDiv.classList.toggle('active', methodDiv.id === `upload-${method}`);
    });
}

// File Upload Handlers
function handleDragOver(e) {
    e.preventDefault();
    elements.uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    elements.uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    elements.uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

async function handleFile(file) {
    // Validate file
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
        showToast('Invalid file type. Please upload JPG, PNG, or WEBP images.', 'error');
        return;
    }
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        showToast('File too large. Maximum size is 10MB.', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        // Upload file
        const formData = new FormData();
        formData.append('image', file);
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || 'Upload failed');
        }
        
        // Store file info
        AppState.uploadedFile = result.file;
        AppState.imageUrl = null;
        
        // Show preview
        showPreview(result.file.url, result.file);
        showToast('File uploaded successfully!', 'success');
        
    } catch (error) {
        console.error('Upload error:', error);
        showToast(`Upload failed: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

async function loadFromUrl() {
    const url = elements.imageUrl.value.trim();
    
    if (!url) {
        showToast('Please enter an image URL', 'error');
        return;
    }
    
    // Basic URL validation
    try {
        new URL(url);
    } catch {
        showToast('Invalid URL format', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        // Test if image loads
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = url;
        });
        
        // Store URL info
        AppState.imageUrl = url;
        AppState.uploadedFile = null;
        
        // Show preview
        showPreview(url, {
            originalName: url.split('/').pop() || 'Image from URL',
            size: 'Unknown',
            url: url
        });
        
        showToast('Image loaded successfully!', 'success');
        
    } catch (error) {
        console.error('URL load error:', error);
        showToast('Failed to load image from URL. Please check the URL.', 'error');
    } finally {
        showLoading(false);
    }
}

function loadExampleUrl(url) {
    elements.imageUrl.value = url;
    loadFromUrl();
}

function showPreview(imageSrc, fileInfo) {
    elements.previewImage.src = imageSrc;
    elements.fileName.textContent = fileInfo.originalName || 'Unknown';
    elements.fileSize.textContent = fileInfo.size ? formatFileSize(fileInfo.size) : 'Unknown';
    
    // Get image dimensions
    elements.previewImage.onload = function() {
        elements.imageDimensions.textContent = `${this.naturalWidth} × ${this.naturalHeight}px`;
    };
    
    elements.previewArea.style.display = 'block';
    elements.nextToSettings.disabled = false;
}

function clearPreview() {
    elements.previewArea.style.display = 'none';
    elements.nextToSettings.disabled = true;
    AppState.uploadedFile = null;
    AppState.imageUrl = null;
    elements.fileInput.value = '';
    elements.imageUrl.value = '';
}

// Background Styles
async function loadBackgroundStyles() {
    try {
        const response = await fetch('/api/process/styles');
        const data = await response.json();
        
        if (data.success) {
            renderStyleGrid(data.styles);
        }
    } catch (error) {
        console.error('Failed to load styles:', error);
        renderDefaultStyles();
    }
}

function renderStyleGrid(styles) {
    const grid = elements.styleGrid;
    grid.innerHTML = '';
    
    Object.entries(styles).forEach(([key, style]) => {
        const div = document.createElement('div');
        div.className = 'style-option';
        div.dataset.style = key;
        
        if (key === AppState.settings.backgroundStyle) {
            div.classList.add('selected');
        }
        
        div.innerHTML = `
            <div class="emoji">${style.preview || '🎨'}</div>
            <div class="name">${style.name}</div>
            <div class="description">${style.description}</div>
        `;
        
        div.addEventListener('click', () => selectStyle(key));
        grid.appendChild(div);
    });
}

function renderDefaultStyles() {
    const defaultStyles = {
        minimalist: { name: "Minimalist", description: "Clean white background", preview: "🤍" },
        luxury: { name: "Luxury", description: "Premium marble surface", preview: "✨" },
        natural: { name: "Natural", description: "Wooden surface", preview: "🌿" },
        modern: { name: "Modern", description: "Contemporary concrete", preview: "🏢" }
    };
    
    renderStyleGrid(defaultStyles);
}

function selectStyle(styleKey) {
    AppState.settings.backgroundStyle = styleKey;
    updateSelectedStyle(styleKey);
}

function updateSelectedStyle(styleKey) {
    document.querySelectorAll('.style-option').forEach(option => {
        option.classList.toggle('selected', option.dataset.style === styleKey);
    });
}

// Navigation
function goToStep(step) {
    if (step < 1 || step > 4) return;
    
    // Validate before moving to next step
    if (step === 2 && !AppState.uploadedFile && !AppState.imageUrl) {
        showToast('Please upload an image first', 'error');
        return;
    }
    
    if (step === 3 && !AppState.settings.apiKey) {
        showToast('Please enter your Runware API key', 'error');
        return;
    }
    
    // Update step indicator
    document.querySelectorAll('.step').forEach((stepEl, index) => {
        stepEl.classList.remove('active', 'completed');
        if (index + 1 < step) {
            stepEl.classList.add('completed');
        } else if (index + 1 === step) {
            stepEl.classList.add('active');
        }
    });
    
    // Show corresponding section
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    const targetSection = document.getElementById(`${getSectionName(step)}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    AppState.currentStep = step;
}

function getSectionName(step) {
    const names = { 1: 'upload', 2: 'settings', 3: 'processing', 4: 'results' };
    return names[step];
}

// Processing
async function startProcessing() {
    // Validate inputs
    if (!AppState.settings.apiKey) {
        showToast('Please enter your Runware API key', 'error');
        return;
    }
    
    if (!AppState.uploadedFile && !AppState.imageUrl) {
        showToast('Please upload an image first', 'error');
        return;
    }
    
    // Go to processing step
    goToStep(3);
    
    // Prepare request data
    const requestData = {
        apiKey: AppState.settings.apiKey,
        backgroundStyle: AppState.settings.backgroundStyle,
        upscaleFactor: AppState.settings.upscaleFactor,
        generateCount: AppState.settings.generateCount,
        finalPolish: AppState.settings.finalPolish
    };
    
    if (AppState.uploadedFile) {
        requestData.filename = AppState.uploadedFile.filename;
    } else {
        requestData.imageUrl = AppState.imageUrl;
    }
    
    try {
        // Start processing
        const response = await fetch('/api/process/single', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || 'Processing failed to start');
        }
        
        AppState.jobId = result.jobId;
        showToast('Processing started successfully!', 'success');
        
        // Start polling for status
        startStatusPolling();
        
    } catch (error) {
        console.error('Processing error:', error);
        showToast(`Failed to start processing: ${error.message}`, 'error');
        showProcessingError(error.message);
    }
}

function startStatusPolling() {
    if (AppState.processingInterval) {
        clearInterval(AppState.processingInterval);
    }
    
    const startTime = Date.now();
    
    AppState.processingInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/status/${AppState.jobId}`);
            const status = await response.json();
            
            if (!response.ok) {
                throw new Error(status.message || 'Failed to get status');
            }
            
            updateProcessingUI(status);
            
            // Update elapsed time
            const elapsed = Math.round((Date.now() - startTime) / 1000);
            elements.elapsedTime.textContent = `${elapsed}s`;
            
            // Check if completed or failed
            if (status.status === 'completed') {
                clearInterval(AppState.processingInterval);
                showProcessingComplete(status.result);
            } else if (status.status === 'failed') {
                clearInterval(AppState.processingInterval);
                showProcessingError(status.error);
            }
            
        } catch (error) {
            console.error('Status polling error:', error);
            clearInterval(AppState.processingInterval);
            showProcessingError('Failed to get processing status');
        }
    }, 2000); // Poll every 2 seconds
}

function updateProcessingUI(status) {
    // Update progress bar
    elements.progressFill.style.width = `${status.progress.percentage}%`;
    elements.progressPercentage.textContent = `${status.progress.percentage}%`;
    elements.progressStatus.textContent = status.currentStep || 'Processing...';
    
    // Update processing steps
    const stepMap = {
        'Background Removal': 'step-bg-removal',
        'Image Enhancement': 'step-enhancement', 
        'Background Generation': 'step-background'
    };
    
    // Reset all steps
    document.querySelectorAll('.processing-step').forEach(step => {
        step.classList.remove('active', 'completed');
    });
    
    // Mark steps based on progress
    if (status.progress.percentage > 0) {
        document.getElementById('step-bg-removal')?.classList.add('completed');
    }
    if (status.progress.percentage > 33) {
        document.getElementById('step-enhancement')?.classList.add('completed');
    }
    if (status.progress.percentage > 66) {
        document.getElementById('step-background')?.classList.add('completed');
    }
    
    // Mark current step as active
    const currentStepElement = document.getElementById(stepMap[status.currentStep]);
    if (currentStepElement && status.progress.percentage < 100) {
        currentStepElement.classList.remove('completed');
        currentStepElement.classList.add('active');
    }
}

function showProcessingComplete(result) {
    goToStep(4);
    
    // Update summary
    elements.resultsSummary.style.display = 'flex';
    elements.totalProcessingTime.textContent = result.metadata.processingTimeFormatted;
    elements.totalCost.textContent = result.metadata.totalCostFormatted;
    elements.imageCount.textContent = result.results.final.length;
    
    // Show results gallery
    renderResultsGallery(result);
    
    // Show footer
    elements.resultsFooter.style.display = 'flex';
    
    showToast('Processing completed successfully!', 'success');
}

function showProcessingError(errorMessage) {
    goToStep(4);
    
    elements.errorDisplay.style.display = 'block';
    elements.errorMessage.textContent = errorMessage;
    elements.resultsSummary.style.display = 'none';
    elements.resultsGallery.innerHTML = '';
    elements.resultsFooter.style.display = 'none';
    
    showToast('Processing failed. Please try again.', 'error');
}

function renderResultsGallery(result) {
    const gallery = elements.resultsGallery;
    gallery.innerHTML = '';
    
    // Add original cutout
    if (result.results.cutout) {
        addResultItem(gallery, 'Product Cutout', result.results.cutout, 'The product with background removed');
    }
    
    // Add enhanced version
    if (result.results.enhanced) {
        addResultItem(gallery, 'Enhanced Product', result.results.enhanced, 'Quality-improved product image');
    }
    
    // Add final results
    result.results.final.forEach((imageUrl, index) => {
        addResultItem(gallery, `Background Variant ${index + 1}`, imageUrl, `Professional product photo with ${result.metadata.backgroundStyle} background`);
    });
}

function addResultItem(container, title, imageUrl, description) {
    const div = document.createElement('div');
    div.className = 'result-item';
    
    div.innerHTML = `
        <img src="${imageUrl}" alt="${title}" loading="lazy">
        <div class="result-item-content">
            <div class="result-item-title">${title}</div>
            <p style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 1rem;">${description}</p>
            <div class="result-item-actions">
                <button class="btn btn-primary btn-sm" onclick="downloadImage('${imageUrl}', '${title}')">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="btn btn-secondary btn-sm" onclick="viewFullSize('${imageUrl}', '${title}')">
                    <i class="fas fa-expand"></i> View
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(div);
}

// Utility Functions
function cancelProcessing() {
    if (AppState.processingInterval) {
        clearInterval(AppState.processingInterval);
    }
    
    if (AppState.jobId) {
        fetch(`/api/status/${AppState.jobId}`, { method: 'DELETE' })
            .catch(error => console.error('Cancel error:', error));
    }
    
    showToast('Processing cancelled', 'info');
    goToStep(2);
}

function startNewProject() {
    // Reset state
    AppState.uploadedFile = null;
    AppState.imageUrl = null;
    AppState.jobId = null;
    
    if (AppState.processingInterval) {
        clearInterval(AppState.processingInterval);
    }
    
    // Clear UI
    clearPreview();
    elements.resultsGallery.innerHTML = '';
    elements.errorDisplay.style.display = 'none';
    
    // Go to first step
    goToStep(1);
}

async function downloadImage(imageUrl, filename) {
    try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${filename.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
        link.click();
        
        URL.revokeObjectURL(link.href);
        showToast('Download started', 'success');
        
    } catch (error) {
        console.error('Download error:', error);
        showToast('Download failed', 'error');
    }
}

function downloadAll() {
    const images = document.querySelectorAll('.result-item img');
    const titles = document.querySelectorAll('.result-item-title');
    
    images.forEach((img, index) => {
        setTimeout(() => {
            const title = titles[index]?.textContent || `Image_${index + 1}`;
            downloadImage(img.src, title);
        }, index * 500); // Stagger downloads
    });
}

function viewFullSize(imageUrl, title) {
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 90vw; max-height: 90vh;">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" style="text-align: center; padding: 0;">
                <img src="${imageUrl}" alt="${title}" style="max-width: 100%; max-height: 70vh; object-fit: contain;">
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Modal Functions
function showSettings() {
    document.getElementById('settingsModal').classList.add('active');
}

function showHelp() {
    document.getElementById('helpModal').classList.add('active');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('active');
}

// API Key Visibility Toggle
function toggleApiKeyVisibility() {
    const input = elements.apiKey;
    const icon = document.getElementById('apiKeyToggle');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// Toast Notifications
function showToast(message, type = 'info', duration = 5000) {
    const container = document.getElementById('toast-container');
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle', 
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    toast.innerHTML = `
        <i class="${iconMap[type]}"></i>
        <span>${message}</span>
    `;
    
    container.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, duration);
}

// Loading Overlay
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// Utility Functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}