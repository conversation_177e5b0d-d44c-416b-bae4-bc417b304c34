/* CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    --secondary: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Grays */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 12px;
    --border-radius-sm: 6px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Animations */
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    min-height: 100vh;
    line-height: 1.6;
    color: var(--gray-700);
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-200);
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* Steps Indicator */
.steps {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: var(--transition);
}

.step.active {
    opacity: 1;
}

.step.completed {
    opacity: 1;
    color: var(--success);
}

.step-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: var(--transition);
}

.step.active .step-icon {
    background: var(--primary);
    color: white;
}

.step.completed .step-icon {
    background: var(--success);
    color: white;
}

.step-label {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Sections */
.section {
    display: none;
}

.section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(1rem); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 2rem;
    border-bottom: 1px solid var(--gray-200);
    text-align: center;
}

.card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.card-header p {
    color: var(--gray-600);
}

.card-body {
    padding: 2rem;
}

.section-footer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
}

/* Upload Section */
.upload-methods {
    margin-bottom: 2rem;
}

.method-tabs {
    display: flex;
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
}

.method-tab {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 2px solid transparent;
}

.method-tab.active {
    border-bottom-color: var(--primary);
    color: var(--primary);
}

.upload-method {
    display: none;
}

.upload-method.active {
    display: block;
}

.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--gray-50);
}

.upload-area:hover {
    border-color: var(--primary);
    background: var(--primary-light);
    background-opacity: 0.05;
}

.upload-area.dragover {
    border-color: var(--primary);
    background: rgba(99, 102, 241, 0.1);
}

.upload-content i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.upload-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.upload-info {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--gray-500);
}

/* URL Input */
.url-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.url-input-group input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.url-examples {
    text-align: center;
}

.url-examples p {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.example-url {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    cursor: pointer;
    font-size: 0.8125rem;
    transition: var(--transition);
}

.example-url:hover {
    background: var(--gray-200);
}

/* Preview */
.preview-area {
    margin-top: 2rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.preview-header h3 {
    font-size: 1rem;
    font-weight: 600;
}

.preview-content {
    display: flex;
    gap: 1rem;
    padding: 1rem;
}

.preview-content img {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.preview-info {
    flex: 1;
}

.info-item {
    display: flex;
    margin-bottom: 0.5rem;
}

.info-item label {
    width: 100px;
    font-weight: 500;
    color: var(--gray-600);
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.form-group label i {
    margin-right: 0.5rem;
    color: var(--gray-500);
}

.required {
    color: var(--error);
}

.input-group {
    display: flex;
    gap: 0.5rem;
}

.input-group input {
    flex: 1;
}

input[type="password"],
input[type="url"],
select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
}

input:focus,
select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

small {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--gray-500);
}

small a {
    color: var(--primary);
    text-decoration: none;
}

/* Style Grid */
.style-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.style-option {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.style-option:hover {
    border-color: var(--primary);
}

.style-option.selected {
    border-color: var(--primary);
    background: rgba(99, 102, 241, 0.05);
}

.style-option .emoji {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.style-option .name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.style-option .description {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.4;
}

/* Advanced Options */
.advanced-options {
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 0.5rem;
}

.option-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.option-row:last-child {
    margin-bottom: 0;
}

.option-row label {
    margin: 0;
    font-weight: 400;
}

.option-row select {
    width: auto;
    min-width: 150px;
}

/* Toggle Switch */
.toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: var(--transition);
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
    background-color: var(--primary);
}

.toggle input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Progress */
.progress-container {
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    width: 0%;
    transition: width 0.5s ease;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

#progressPercentage {
    font-weight: 600;
    color: var(--primary);
}

/* Processing Steps */
.processing-steps {
    margin: 2rem 0;
}

.processing-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    background: var(--gray-50);
    transition: var(--transition);
}

.processing-step.active {
    background: rgba(99, 102, 241, 0.1);
    border-left: 4px solid var(--primary);
}

.processing-step.completed {
    background: rgba(16, 185, 129, 0.1);
    border-left: 4px solid var(--success);
}

.processing-step .step-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: var(--gray-300);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.processing-step.active .step-icon {
    background: var(--primary);
    color: white;
}

.processing-step.completed .step-icon {
    background: var(--success);
    color: white;
}

.processing-step .step-content h4 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.processing-step .step-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.processing-step .step-status i {
    color: var(--gray-400);
}

.processing-step.active .step-status i {
    color: var(--primary);
    animation: spin 1s linear infinite;
}

.processing-step.completed .step-status i {
    color: var(--success);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Cost Tracker */
.cost-tracker {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    margin: 2rem 0;
}

.cost-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.cost-item label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.cost-item span {
    font-weight: 600;
    color: var(--gray-900);
}

/* Results */
.results-summary {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.summary-item label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.summary-item span {
    font-weight: 600;
    color: var(--gray-900);
}

.results-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.result-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: white;
    transition: var(--transition);
}

.result-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.result-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.result-item-content {
    padding: 1rem;
}

.result-item-title {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.result-item-actions {
    display: flex;
    gap: 0.5rem;
}

/* Error Display */
.error-display {
    text-align: center;
    padding: 3rem 2rem;
}

.error-content i {
    font-size: 3rem;
    color: var(--error);
    margin-bottom: 1rem;
}

.error-content h3 {
    margin-bottom: 0.5rem;
    color: var(--gray-900);
}

.error-content p {
    margin-bottom: 2rem;
    color: var(--gray-600);
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    text-align: right;
}

/* Settings */
.setting-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.setting-group:last-child {
    margin-bottom: 0;
}

/* Help */
.help-content {
    max-height: 400px;
    overflow-y: auto;
}

.help-step {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.help-step:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.help-step h4 {
    margin-bottom: 0.5rem;
    color: var(--primary);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
}

.toast {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    border-left: 4px solid var(--info);
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left-color: var(--success);
}

.toast.error {
    border-left-color: var(--error);
}

.toast.warning {
    border-left-color: var(--warning);
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1200;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .steps {
        gap: 1rem;
    }
    
    .step-label {
        display: none;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .section-footer {
        flex-direction: column;
    }
    
    .method-tabs {
        flex-direction: column;
    }
    
    .preview-content {
        flex-direction: column;
    }
    
    .preview-content img {
        width: 100%;
        height: 200px;
    }
    
    .results-gallery {
        grid-template-columns: 1fr;
    }
    
    .results-summary,
    .cost-tracker {
        flex-direction: column;
        gap: 1rem;
    }
    
    .option-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .url-input-group {
        flex-direction: column;
    }
}